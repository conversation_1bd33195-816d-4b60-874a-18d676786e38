<div class="campningDiv indata">
    <div class="media-data">
        <div class="media-data-inner">
            @if ($socialPost->type == 'photo')
                <img src="{{ $socialPost->link_url }}">
            @elseif($socialPost->type == 'video')
                <video>
                    <source src="{{ $socialPost->link_url }}" type="video/mp4">
                </video>
            @endif
        </div>
    </div>
    <div class="post-text">
        <div class="post-title">
            @if (isset($socialPost->text) && $socialPost->text != '')
                {{ $socialPost->text }}
            @endif
        </div>
        <div class="post-link">
            @if (isset($socialPost->link) && $socialPost->link != '')
                @if (str_contains($socialPost->link, 'http'))
                    <a class="view-link" href="{{ $socialPost->link }}" target="_blank">View</a>
                @elseif($socialPost->thumbnail != '')
                    <a class="view-link" href="{{ $socialPost->thumbnail }}" target="_blank">View</a>
                @endif
            @endif
        </div>
    </div>
    <div class="publishfirDaat">
        @if (isset($socialPost->published_at) && $socialPost->published_at != '')
            @php
                $msg_time = \DateTime::createFromFormat('Y-m-d H:i:s', $socialPost->published_at, new DateTimeZone('UTC'));
                $msg_time->setTimeZone(new DateTimeZone(@Session::get('timezone') ? Session::get('timezone') : 'UTC'));
                $msg_time_formatted = $msg_time->format('M d,Y H:i');
                $now = new \DateTime('now', new \DateTimeZone(@\Session::get('timezone') ?: 'UTC'));

                // Subtract 1 days from $now
                $threeDaysAgo = (clone $now)->sub(new \DateInterval('P1D'));
                $interval = $threeDaysAgo->diff($msg_time);
            @endphp
            {{ $msg_time_formatted }}
            @if ((
                $influencerRequestDetail->advertising == 'Story' ||
                $influencerRequestDetail->advertising == 'Story - Video' ||
                $influencerRequestDetail->advertising == 'Story - Picture') &&
                $msg_time > $threeDaysAgo
            )
                <span class="submission-timing story_timer{{ $socialPost->id }}" style="margin-left:65px !important;"></span>
            @endif
        @endif
    </div>
    @php $content = $influencerRequestDetail->post_content_type ; @endphp
    @if ((
        $influencerRequestDetail->advertising == 'Story' ||
        $influencerRequestDetail->advertising == 'Story - Video' ||
        $influencerRequestDetail->advertising == 'Story - Picture') &&
        $msg_time > $threeDaysAgo
    )
        <button class="cunformBtn confirmBtn requestSubmit" disabled style="border:#0d0e0d !important;background:#0d0e0d !important;">Confirm</button>
    @else
        <button
            class="cunformBtn confirmBtn requestSubmit"
            target="popup"
            data-bs-toggle="modal"
            data-bs-target="#influencerConfirmCampaignSubmission{{ $influencerRequestDetail->id }}"
            data-influencer_request_id='{!! json_encode($influencerRequestDetail->id) !!}'
            data-post_id='{!! json_encode($socialPost->id) !!}'
            data-advertising='{!! json_encode($influencerRequestDetail->advertising) !!}'
            data-media='{!! json_encode($influencerRequestDetail->media) !!}'
            data-text='{!! json_encode($socialPost->text) !!}'
            data-media_url='{!! json_encode(isset($socialPost->link) ? $socialPost->link : '') !!}'
            data-type='{!! json_encode(isset($socialPost->type) ? $socialPost->type : '') !!}'
            data-post_content_type='{!! json_encode($content) !!}'
            data-published_at='{!! json_encode($socialPost->published_at) !!}'
            data-post_type='{!! json_encode($influencerRequestDetail->post_type) !!}'
            data-thumbnail='{!! json_encode(isset($socialPost->thumbnail) ? $socialPost->thumbnail : '') !!}'
            data-like='{!! json_encode($socialPost->like) !!}'
            data-comment='{!! json_encode($socialPost->comment) !!}'
            data-view='{!! json_encode($socialPost->view) !!}'
            data-share='{!! json_encode($socialPost->share) !!}'
            data-friend='{!! json_encode($socialPost->friend) !!}'
            data-favorite='{!! json_encode($socialPost->favorite) !!}'
            onclick="confirmInfluencerSubmission(this)">Confirm
        </button>
    @endif
</div>