
@php

$Influencer = App\Models\InfluencerDetail::where('user_id',Auth::id())->first(); 

$tot_count = App\Models\InfluencerRequestDetail::where('influencer_detail_id',@$Influencer->id)
                ->where('finish',NULL)->where('refund_reason',NULL)
                ->where(function ($query) {
                    $query->where('review', '!=', 1) ;
                })
                ->count(); 
$open_count =   App\Models\InfluencerRequestDetail::join('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id' )  
                 ->select('influencer_request_details.*')
                 ->where('influencer_request_details.influencer_detail_id',@$Influencer->id)
                 ->where('influencer_request_details.finish',NULL)
                 ->where('influencer_request_details.refund_reason',NULL) 
                ->where(function ($query) {
                    $query->where('influencer_request_details.review', '=', NULL)
                          ->orWhere('influencer_request_details.review', '=', 0);
                })            
                 ->count();  


$req_count =$tot_count ; 


$myCampaignList =    App\Models\InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id' )   
            ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id' )    
            ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id' )  
             ->select('influencer_request_details.*', 'influencer_details.id as i_id','influencer_details.user_id as i_user_id','influencer_request_accepts.request','influencer_request_accepts.request_time','influencer_request_accepts.request_time_accept','influencer_request_accepts.id as influencer_request_accept_id' ,'influencer_request_accepts.created_at as accept_time')
            ->where('influencer_details.user_id',Auth::id())
            ->orderBy('influencer_request_details.id','desc')->get();    

$postCount = 0 ; 
$postCountReq = 0 ; 
foreach($myCampaignList as $row){
    if($row->request == 1 &&  $row->invoice_id!='' && $row->review !='1' && $row->review !='0'  &&   $row->refund_reason =='' ){ 
            $postCountReq++;
            if($row->invoice_id!=''){
                $postCount++;
            } 
    }

}  

$reqCountList =   App\Models\InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id' )   
            ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id' )    
            ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id' )  
             ->select('influencer_request_details.*', 'influencer_details.id as i_id','influencer_details.user_id as i_user_id','influencer_request_accepts.request','influencer_request_accepts.request_time','influencer_request_accepts.request_time_accept','influencer_request_accepts.id as influencer_request_accept_id' )
            ->where('influencer_details.user_id',Auth::id())
            ->orderBy('influencer_request_details.id','desc')->get() ;

$campaignRequestTime = App\Models\CampaignRequestTime::first();            

 $reqCount = 0 ;
 $openCountReq = 0 ;
foreach($reqCountList as $row){
    if($row->invoice_id=='' && $row->review != '2' && $row->refund_reason == ''){ 
        $time = (isset($row->request_time_accept) && $row->request_time_accept == 1)?$row->request_time+$row->time:$row->time;

        $created_date =  date('Y-m-d H:i:s',strtotime($row->created_at));
        $updated_date =  date('Y-m-d H:i:s',strtotime($row->updated_at));
        $campaignDate= date('Y-m-d H:i:s', strtotime($created_date. ' + '.$campaignRequestTime->request_time.' days'));  
        $date = date('Y-m-d H:i:s'); 
        $seconds = strtotime($campaignDate) - strtotime($date);

        $days    = floor($seconds / 86400);  
        if($date <= $campaignDate ){  
                $openCountReq++; 
                $reqCount++; 
        }
    }

}
$tot = $reqCount; 
$req_count = $postCountReq+$openCountReq;
@endphp
<div class="connectPrising advertisingSelect ">
    @if ($user->activate != '2')
        <input type="hidden" name="collection"  value="">  
    @else
        <input type="hidden" name="collection"  value="4">  
    @endif
    <input type="hidden" name="check-status"  value="{{ (isset($advertising_methods[0]->media) && $advertising_methods[0]->media == 'facebook') || (isset($advertising_methods[0]->media) && $advertising_methods[0]->media == 'instagram') || (isset($advertising_methods[0]->media) && $advertising_methods[0]->media == 'tiktok') || (isset($advertising_methods[0]->media) && $advertising_methods[0]->media == 'youtube') ?'selected':'' }}">  

    <div class="publish-outer">
        <div class="active-inactive">
            <div class="active-inactive-inner">
                {{-- <label>You are</label> --}}
                <div class="active-inactive-input">
                    <input type="checkbox" name="status" id="user_status" value="1" @if(Auth::user()->status==1 && $req_count < 5) checked @endif >
                    <div class="input-label">
                        <img src="{{ asset('/') }}/assets/front-end/images/icons/icon-wifi.svg" class="" alt="">
                        <span class="active-inactive-text"></span>
                    </div>
                </div>
            </div>
        </div>
        
        <div id="usr_online" @if(!(Auth::user()->status==1 && $req_count < 5)) style="display: none;" @endif>
            <div class="row pt-5 justify-content-center">
                <div class="col-12 col-sm-4 col-md-2 text-center">
                    <img src="{{ asset('/') }}assets/front-end/images/icons/online_inf_icon1.png" alt="">
                </div>
                <div class="col-12 col-sm-4 col-md-2 text-center">
                    <img src="{{ asset('/') }}assets/front-end/images/icons/online_inf_icon2.png" alt="">
                </div>
                <div class="col-12 col-sm-4 col-md-2 text-center">
                    <img src="{{ asset('/') }}assets/front-end/images/icons/online_inf_icon3.png" alt="">
                </div>
            </div>
            <div class="row pt-5 justify-content-center">
                <div class="col-12 text-center">
                    <h3>You can currently be requested by brands.</h3>
                </div>
            </div>
        </div>
        
        <div id="usr_offline"  @if((Auth::user()->status==1 && $req_count < 5)) style="display: none;" @endif>
            <div class="row pt-4 justify-content-center">
                <div class="col-12 col-sm-4 col-md-2 text-center">
                    <img src="{{ asset('/') }}assets/front-end/images/icons/offline_inf_icon1.png" alt="">
                </div>
                <div class="col-12 col-sm-4 col-md-2 text-center">
                    <img src="{{ asset('/') }}assets/front-end/images/icons/offline_inf_icon2.png" alt="">
                </div>
                <div class="col-12 col-sm-4 col-md-2 text-center">
                    <img src="{{ asset('/') }}assets/front-end/images/icons/offline_inf_icon3.png" alt="">
                </div>
            </div>
            <div class="row pt-5 justify-content-center">
                <div class="col-12 text-center">
                    <h3>You cannot be requested until you switch back online.</h3>
                </div>
            </div>
        </div>
        
    </div>
</div>

<div class="step-nevigationbutton">
    <div class="nav-left back me-2" id="Back_step4">
        <img src="{{ asset('/') }}assets/front-end/images/icons/step-left.svg" class="" alt="">
    </div>
    <div class="nav-right next ms-auto" >
        <img src="{{ asset('/') }}assets/front-end/images/icons/step-right.svg" class="" alt="" style="background: #d5cbe9 !important;cursor:default;">
    </div>
</div>

<script type="text/javascript">

$('#customCheck').click(function(){
    if($(this).is(':checked') == true)
    {
        $('#additional_price').removeAttr('readonly');
        $('#additional_price').attr('required','');
    }else{
        $('#additional_price').attr('readonly', '');
        $('#additional_price').removeAttr('required');
        $('#additional_price').val('');
    }
});

$(document).ready(function(){
    if($('#customCheck').is(':checked') == true)
    {
        $('#additional_price').removeAttr('readonly');
        $('#additional_price').attr('required','');
    }else{
        $('#additional_price').attr('readonly', '');
        $('#additional_price').removeAttr('required');
        $('#additional_price').val('');
    }
});

$("#storlekslider" ).slider({
    range: "max",
    min: 5,
    max: 120,
    step: 1,
    value: 5,
    slide: function( event, ui ) {
        //var value1 = $("#storlekslider").slider("value");  
        // $("#storlek_testet").val( ui.value );
        $(ui.value).val(minutes);
        $("#storlekslider").find(".ui-slider-handle").text(minutes +' minutes');  
    }
});

$("#storlekslider").find(".ui-slider-handle").text('5 minutes');

$('#exampleModaladd').on('hidden.bs.modal', function () {
    $("#storlekslider").find(".ui-slider-handle").text("5 minutes")
});

function openModal(){
    console.log($("#video_price").val());
    console.log($("#video_price_additional").val());
    $('.price_value').html($("#video_price").val());
    $("#storlek_testet").val($("#video_price").val());
    $("#storlek_testet_price_additional").val($("#video_price_additional").val());
    $( "#storlekslider" ).slider({
        range: "max",
        min: 5,
        max: 120,
        step: 1,
        value: 5,
        slide: function( event, ui ) {
            //var value1 = $("#storlekslider").slider("value");  
            // $("#storlek_testet").val( ui.value );
            $(ui.value).val(5);
            $("#storlekslider").find(".ui-slider-handle").text('5 minutes');  
        }
    });
}

function openModal2(){
    console.log($("#livestream_price").val());
    console.log($("#livestream_price_additional").val());
    $('.price_value').html($("#livestream_price").val());
    $("#storlek_testet").val($("#livestream_price").val());
    $("#storlek_testet_price_additional").val($("#livestream_price_additional").val());
    $( "#storlekslider" ).slider({
        range: "max",
        min: 5,
        max: 120,
        step: 1,
        value: 5,
        slide: function( event, ui ) {
            //var value1 = $("#storlekslider").slider("value");  
            // $("#storlek_testet").val( ui.value );
            $(ui.value).val(5);
            $("#storlekslider").find(".ui-slider-handle").text('5 minutes');  
        }
    });
}



function videoRange(action){
    // $('#btnRange1').click(function() {
    var direction = action;
    var value =  $("#storlekslider").slider("value");
    var value1 =  $("#storlek_testet").val();
    var value2 =  $("#storlek_testet_price_additional").val();
    console.log('value'+value);
    console.log('value1'+value1);
    console.log('value2'+value2);
    if (direction == "plus") {
        if(parseInt(value) < 125){
            // console.log(parseInt(value1)+parseInt(value2));
            $('.price_value').html(parseInt(value1)+parseInt(value2));
            $("#storlek_testet").val(parseInt(value1)+parseInt(value2));
            $("#storlekslider").slider("value", value+5);
        }
    } else {
        if(value > 5){
            $('.price_value').html(parseInt(value1)-parseInt(value2));
            $("#storlek_testet").val(parseInt(value1)-parseInt(value2));
            $("#storlekslider").slider("value", parseInt(value)-5);
        }
    }

    var currentVal = $("#storlekslider").slider("value");
    $("#storlekslider").find(".ui-slider-handle").text(currentVal+" Minutes");
}
</script>
