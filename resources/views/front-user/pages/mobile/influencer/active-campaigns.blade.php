<div class="campaign-header">
    <div class="row"
        style="margin-top: -18px; margin-left: 0px; margin-right: 0px; cursor: pointer;"
        data-bs-toggle="modal"
        data-bs-target="#campaignPhasesModal">
        @php
            if(isset($influencerDataItem->social_post_id) && $influencerDataItem->social_post_id != '') {
                $tagClassName = "btn-review-phase";
                $tagName = "Review Phase";
            }
            else {
                $tagClassName = "btn-submit-phase";
                $tagName = "Submit Phase";
            }
        @endphp
        <span class={{ $tagClassName }}
            style="width: 100%; padding: 5px;display: flex;justify-content: space-between;">
            <span style="text-align: left;">{{ $tagName }}</span>
            <span style="text-align: right;">ID # {{ $influencerDataItem->campaign_id }}</span>
        </span><br>
    </div>
    <div class="header-section" style=" margin-bottom: 30px">
        <div class="row" style="padding-left:20px; width:100%">
            <div class="col-9">
                <div class="row">
                    <h4 class="truncate-text" style="font-weight: 700; font-size: 15px; padding-top: 10px; margin-left: -14px;">
                        {{ $influencerDataItem->compaign_title }}
                    </h4>
                </div>
                <div class="row" style="width:100%">
                    <span class="col-auto"
                        style="font-size: 7px !important; padding: 0 5px 0 0;"><img
                            src="{{ asset('/') }}/assets/front-end/images/new/brand_camp.svg"
                            width="15" height="15">&nbsp;
                        {{ $influencerDataItem->user->first_name }}
                        {{ $influencerDataItem->user->last_name }}
                    </span>
                    <span class="col-auto"
                        style="font-size: 7px !important; padding: 0 5px 0 0;"><img
                            src="{{ asset('/') }}/assets/front-end/images/new/survey_camp.svg"
                            width="15" height="15">&nbsp;
                        {{ $influencerDataItem->advertising }}
                    </span>
                    <span class="col-auto"
                        style="font-size: 7px !important; padding: 0 5px 0 0;">
                        <img src="{{ asset('/') }}/assets/front-end/images/icons/campaigns-{{ $influencerDataItem->media }}.svg"
                            alt="" width="15"
                            height="15">&nbsp;{{ $influencerDataItem->post_type }}
                    </span>
                </div>
                <div class="row" style="padding-top:5px;">
                    <span class="text-success"
                        style="font-size: 16px;font-weight: 700; width:auto;">€
                        {{ number_format($influencerDataItem->current_price, 2) }}</span>
                    <span class="submit-text-color" style=" width:auto;">
                        @php
                            $time =
                                isset($influencerDataItem->request_time_accept) &&
                                $influencerDataItem->request_time_accept == '1'
                                    ? $influencerDataItem->request_time + $influencerDataItem->time
                                    : $influencerDataItem->time;

                            $created_date = date(
                                'Y-m-d H:i:s',
                                strtotime($influencerDataItem->created_at),
                            );
                            $updated_date = date(
                                'Y-m-d H:i:s',
                                strtotime($influencerDataItem->updated_at),
                            );
                            $campaignDate = date(
                                'Y-m-d H:i:s',
                                strtotime(
                                    $updated_date . ' + ' . $time . ' days',
                                ),
                            );
                            $date = date('Y-m-d H:i:s');
                            $seconds =
                                strtotime($campaignDate) - strtotime($date);

                            $days = floor($seconds / 86400);
                            if ($days < $time && $days >= 0) {
                                $hours = floor(
                                    ($seconds - $days * 86400) / 3600,
                                );

                                $minutes = floor(
                                    ($seconds - $days * 86400 - $hours * 3600) /
                                        60,
                                );

                                $seconds = floor(
                                    $seconds -
                                        $days * 86400 -
                                        $hours * 3600 -
                                        $minutes * 60,
                                );
                            } else {
                                'TIme Passed';
                            }
                        @endphp

                        <span class="timing" style="font-size: 14px; width: auto;"
                            id="mobiletimer{{ $influencerDataItem->campaign_id }}"></span>
                    </span>
                </div>
            </div>
            <div class="col-3"
                style="display:flex; align-items:center; justify-content:center; flex-direction:column; padding: 0 0px; margin: 0 0px;">
                <button class="btn  btn-show-details" target="popup"
                    data-bs-toggle="modal"
                    data-bs-target="#requestForm{{ $influencerDataItem->id }}"
                    style="width: 100% !important; padding: 0; margin: 2px 0; font-size: 10px">Show Details
                </button>
                @foreach ($influencerRequestDetails as $influencerRequestDetail)
                    {{-- TODO A temporary fix for badly/incomplete deletion of influencer data from admin panel  --}}
                    @if (empty($influencerRequestDetail->influencerdetails))
                        @php continue; @endphp
                    @endif
                    @if (empty($influencerRequestDetail->invoices->receipt))
                    @php continue; @endphp
                    @endif
                    <a class="btn btn-show-my-invoice" href="{{ $influencerRequestDetail->invoices->receipt }}" target="_blank"
                        style="width: 100% !important; padding: 0; margin: 2px 0; font-size: 10px; border: solid 1px #AD80FF; color: white; background-color:#AD80FF;">My Invoice
                    </a>
                    @php break; @endphp
                @endforeach
                <button class="btn btn-cancel-new" target="popup"
                    data-bs-toggle="modal"
                    data-bs-target="#cancelRequest{{ $influencerDataItem->id }}"
                    style="width: 100% !important; padding: 0; margin: 2px 0; font-size: 10px">Cancel
                </button>
                <button
                    class="btn btn-show-result1 startTImer{{ $influencerDataItem->campaign_id }}"
                    value="Submit"
                    onclick="influencerInitiateCampaignSubmissionSubmitButton('{{ $influencerDataItem->id }}')"
                    style="width: 100% !important; padding: 0; margin: 2px 0; font-size: 10px">Submit
                </button>
            </div>
        </div>
    </div>
</div>
