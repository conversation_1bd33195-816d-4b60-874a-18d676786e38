@if(Session::get('influencer_error') && Session::get('influencer_error')!='') 
    <script type="text/javascript">
        $('#confirmreset').modal('show');
        $('#errorTextModal').html("{{ Session::get('influencer_error')}}");
    </script>
@endif 

@if(Session::get('influencer_follower_error') && Session::get('influencer_follower_error')!='') 
    <script type="text/javascript">
        $('#confirmreset').modal('show');
        $('#errorTextModal').html("{{ Session::get('influencer_follower_error')}}");
    </script>
@endif 

<input type="hidden" value="{{$social_connect}}" id="social_connect_check">
    <span id="errorText"></span>    
    
    @if(isset($social_connect_youtube))
        @php $count = 0; @endphp
            @foreach($sm_campaigns as $campaign)
                @if($campaign->media == 'Youtube' && $campaign->type == 'influencer') 
                    @php $count++; @endphp 
                @endif
            @endforeach

        <div class="connectWithInner d-flex align-items-center disconnectedToSocial @if($count == 0 ) blur @endif"> 
            <div class="socialCnt"> 
                @if($count == 0 )
                <img src="{{ asset('/') }}/assets/front-end/images/col_icon_youtube-black.png" class="iconBlack" alt="">
                @else
                <img src="{{ asset('/') }}/assets/front-end/images/icons/col_icon_youtube.png" class="icon" alt="">
                @endif
            </div>
            <div class="conttr">
                <div class="socialUserImage">
                    <a href="{{$social_connect_youtube->url}}" target="_blank"><img src="{{ asset('storage/' . $social_connect_youtube->picture) }}" alt=""></a>
                </div>
                <div class="socialUserDetail">
                    <div class="socialUserDetailName"><a href="{{$social_connect_youtube->url}}" target="_blank">{{$social_connect_youtube->name}}</a></div>
                    <div class="socialUserDetailNumber">{{  ($social_connect_youtube->followers!='')?$social_connect_youtube->followers:0  }} Followers</div>
                </div>
            </div>
            @if($count == 0 )
            <div class="cna">Currently not available</div>
            @endif
            <div class="socialBtnConnect">
                <a href="{{url('disconnect/youtube')}}" class="soclBtn disconnect table-btn red-btn">Disconnect</a>
            </div> 
        </div> 
    @endif


    @if(isset($social_connect_facebook)) 
        @php $count = 0; @endphp
        @foreach($sm_campaigns as $campaign)
            @if($campaign->media == 'Facebook' && $campaign->type == 'influencer') 
                @php $count++; @endphp 
            @endif
        @endforeach 
        <div class="connectWithInner d-flex align-items-center disconnectedToSocial @if($count == 0 ) blur @endif">
            <div class="socialCnt"> 
                @if($count == 0 )
                <img src="{{ asset('/assets/front-end/images/col_icon_facebook-black.png') }}" class="iconBlack" alt="">
                @else
                <img src="{{ asset('/assets/front-end/images/icons/col_icon_facebook.png') }}" class="icon" alt="">
                @endif
            </div>
            <div class="conttr">
                <div class="socialUserImage">
                    <a href="{{$social_connect_facebook->url}}" target="_blank"><img src="{{ asset('storage/' . $social_connect_facebook->picture) }}" alt=""></a>
                </div>
                <div class="socialUserDetail">
                    <div class="socialUserDetailName"><a href="{{$social_connect_facebook->url}}" target="_blank">{{$social_connect_facebook->name}}</a></div>
                    <div class="socialUserDetailNumber">{{  ($social_connect_facebook->followers!='')?$social_connect_facebook->followers:0  }} Followers</div>
                </div>
            </div>
            @if($count == 0 )
            <div class="cna">Currently not available</div>
            @endif
            <div class="socialBtnConnect">
                <a href="{{url('disconnect/facebook')}}" class="soclBtn disconnect table-btn red-btn">Disconnect</a>
            </div> 
        </div> 
    @endif
   
    @if(isset($social_connect_instagram)) 
        @php $count = 0; @endphp
        @foreach($sm_campaigns as $campaign)
            @if($campaign->media == 'Instagram' && $campaign->type == 'influencer') 
                @php $count++; @endphp 
            @endif
        @endforeach
         
        <div class="connectWithInner d-flex align-items-center disconnectedToSocial @if($count == 0 ) blur @endif">
            <div class="socialCnt"> 
                @if($count == 0 )
                    <img src="{{ asset('/assets/front-end/images/col_icon_instagram-black.png') }}" class="iconBlack" alt="">
                @else
                    <img src="{{ asset('/assets/front-end/images/icons/col_icon_instagram.png') }}" class="icon" alt="">
                @endif
            </div>
            <div class="conttr">
                <div class="socialUserImage">
                    <a href="{{$social_connect_instagram->url}}" target="_blank"> <img src="{{ asset('storage/' . $social_connect_instagram->picture) }}" alt=""></a>
                </div>
                <div class="socialUserDetail">
                    <div class="socialUserDetailName"><a href="{{$social_connect_instagram->url}}" target="_blank">{{$social_connect_instagram->name}}</a></div>
                    <div class="socialUserDetailNumber">{{  ($social_connect_instagram->followers!='')?$social_connect_instagram->followers:0  }} Followers</div>
                </div>
            </div>
            @if($count == 0 )
            <div class="cna">Currently not available</div>
            @endif
            <div class="socialBtnConnect">
                <a href="{{url('disconnect/instagram')}}" class="soclBtn disconnect table-btn red-btn">Disconnect</a>
            </div> 
        </div> 
    @endif
   

    @if(isset($social_connect_tiktok)) 
        @php $count = 0; @endphp
        @foreach($sm_campaigns as $campaign)
            @if($campaign->media == 'Tiktok' && $campaign->type == 'influencer') 
                @php $count++; @endphp 
            @endif
        @endforeach 

        <div class="connectWithInner d-flex align-items-center disconnectedToSocial @if($count == 0 ) blur @endif">
            <div class="socialCnt"> 
                @if($count == 0 )
                <img src="{{ asset('/') }}/assets/front-end/images/col_icon_tiktok-black.png" class="iconBlack" alt="">
                @else
                <img src="{{ asset('/') }}/assets/front-end/images/icons/col_icon_tiktok.png" class="icon" alt="">
                @endif
            </div>
            <div class="conttr">
                <div class="socialUserImage">
                    <a href="{{$social_connect_tiktok->url}}" target="_blank"> <img src="{{asset('storage/'.$social_connect_tiktok->picture)}}" alt=""></a>
                </div>
                <div class="socialUserDetail">
                    <div class="socialUserDetailName"><a href="{{$social_connect_tiktok->url}}" target="_blank">{{$social_connect_tiktok->name}}</a></div>
                    <div class="socialUserDetailNumber">{{  ($social_connect_tiktok->followers!='')?$social_connect_tiktok->followers:0  }} Followers</div>
                </div>
            </div>
            @if($count == 0 )
            <div class="cna">Currently not available</div>
            @endif
            <div class="socialBtnConnect">
                <a href="{{url('disconnect/tiktok')}}" class="soclBtn disconnect table-btn red-btn">Disconnect</a>
            </div> 
        </div> 
    @endif  


    @if(!isset($social_connect_instagram)) 
    @php $count = 0; @endphp
    @foreach($sm_campaigns as $campaign)
        @if($campaign->media == 'Instagram' && $campaign->type == 'influencer') 
            @php $count++; @endphp 
        @endif
    @endforeach
    


    <div class="connectWithInner d-flex align-items-center connectedToSocial @if($count == 0 ) blur @endif">
        <div class="socialCnt"> 
            <img src="{{ asset('/assets/front-end/images/col_icon_instagram-black.png') }}" class="iconBlack" alt="">
        </div> 
        <div class="socialUserImage"></div> 
        @if($count == 0 )
            <div class="cna">Currently not available</div>
        @endif
        <div class="socialBtnConnect">
            <a target="popup" data-bs-toggle="modal" data-bs-target="#instagramDialogue" class="soclBtn connect table-btn green-btn"  @if($count == 0 ) disabled @endif >Connect</a>
        </div> 
    </div> 
@endif 
     @if(!isset($social_connect_facebook)) 
        @php $count = 0; @endphp
        @foreach($sm_campaigns as $campaign)
            @if($campaign->media == 'Facebook' && $campaign->type == 'influencer') 
                @php $count++; @endphp 
            @endif
        @endforeach
       


        <div class="connectWithInner d-flex align-items-center connectedToSocial @if($count == 0 ) blur @endif">
            <div class="socialCnt"> 
                <img src="{{ asset('/assets/front-end/images/col_icon_facebook-black.png') }}" class="iconBlack" alt="">
            </div> 
            <div class="socialUserImage"></div> 
            @if($count == 0 )
                <div class="cna">Currently not available</div>
            @endif
            <div class="socialBtnConnect">
                <a target="popup" data-bs-toggle="modal" data-bs-target="#facebookDialogue" class="soclBtn connect table-btn green-btn"  @if($count == 0 ) disabled @endif >Connect</a>
            </div> 
        </div>
    @endif   
    @if(!isset($social_connect_youtube))
    @php $count = 0; @endphp
    @foreach($sm_campaigns as $campaign)
        @if($campaign->media == 'Youtube' && $campaign->type == 'influencer') 
            @php $count++; @endphp 
        @endif
    @endforeach
    
    <div class="connectWithInner d-flex align-items-center connectedToSocial @if($count == 0 ) blur @endif"> 
        <div class="socialCnt"> 
            <img src="{{ asset('/') }}/assets/front-end/images/col_icon_youtube-black.png" class="iconBlack" alt="">
        </div> 
        <div class="socialUserImage">
            {{-- <img src="{{ asset('/') }}/assets/front-end/images/userImage.jpg" alt=""> --}}
        </div> 
        @if($count == 0 )
        <div class="cna">Currently not available</div>
        @endif
        <div class="socialBtnConnect">
            <a target="popup" data-bs-toggle="modal" data-bs-target="#youtubeDialogue" class="soclBtn connect table-btn green-btn"  @if($count == 0 ) disabled @endif >Connect</a>
            <!-- <a  target="popup" onclick="window.open('{{url('connect/youtube')}}','name','width=550,height=500,left=400,top=150,toolbar=0,status=0')"  class="soclBtn connect table-btn green-btn">Connect</a> -->
        </div> 
    </div>
@endif
     @if(!isset($social_connect_tiktok)) 

        @php $count = 0; @endphp
        @foreach($sm_campaigns as $campaign)
            @if($campaign->media == 'Tiktok' && $campaign->type == 'influencer') 
                @php $count++; @endphp 
            @endif
        @endforeach
        

        <div class="connectWithInner d-flex align-items-center connectedToSocial @if($count == 0 ) blur @endif">
            <div class="socialCnt"> 
                <img src="{{ asset('/') }}/assets/front-end/images/col_icon_tiktok-black.png" class="iconBlack" alt="">
            </div>
            <div class="socialUserImage">
                {{-- <img src="{{ asset('/') }}/assets/front-end/images/userImage.jpg" alt=""> --}}
            </div>
            @if($count == 0 )
                <div class="cna">Currently not available</div>
            @endif
            <div class="socialBtnConnect">
                <a target="popup" data-bs-toggle="modal" data-bs-target="#tiktokDialogue" class="soclBtn connect table-btn green-btn"  @if($count == 0 ) disabled @endif >Connect</a> 
            </div> 
        </div>
    @endif

<!-- Modal -->
@php $dialogues = DB::table('dialogues')->where('name', 'twitterDialogue')->first(); @endphp
    <div class="modal fade influncer" id="twitterDialogue" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="twitterDialogueLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-body">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><img src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg" alt=""></button>
                    <div class="wizardHeading">{{$dialogues->heading}}</div>
                    <div class="wizardForm">
                       {!!$dialogues->content!!}
                         
                        <div class="popup2btns d-flex">
                            <a href="#" class="et-submit mx-3 confirm-link"   onclick="window.open('{{url('connect/twitter')}}','name','width=550,height=500,left=400,top=150,toolbar=0,status=0')"    data-bs-dismiss="modal" aria-label="Close">Confirm</a>
                            <a href="#" class="et-submit red-btn mx-3 confirm-link cancleBtn" data-bs-dismiss="modal" aria-label="Close">Cancel</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Modal -->
@php $dialogues = DB::table('dialogues')->where('name', 'youtubeDialogue')->first(); @endphp
    <div class="modal fade influncer" id="youtubeDialogue" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="youtubeDialogueLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-body">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><img src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg" alt=""></button>
                    <div class="wizardHeading">{{$dialogues->heading}}</div>
                    <div class="wizardForm">
                        {!!$dialogues->content!!}
                        
                        <div class="popup2btns d-flex">
                            <a href="#" class="et-submit mx-3 confirm-link"   onclick="window.open('{{url('connect/youtube')}}','name','width=550,height=500,left=400,top=150,toolbar=0,status=0')"    data-bs-dismiss="modal" aria-label="Close">Confirm</a>
                            <a href="#" class="et-submit red-btn mx-3 confirm-link cancleBtn" data-bs-dismiss="modal" aria-label="Close">Cancel</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Modal -->
@php $dialogues = DB::table('dialogues')->where('name', 'twitchDialogue')->first(); @endphp
    <div class="modal fade influncer" id="twitchDialogue" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="twitchDialogueLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-body">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><img src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg" alt=""></button>
                    <div class="wizardHeading">{{$dialogues->heading}}</div>
                    <div class="wizardForm">
                        {!!$dialogues->content!!}
                        
                        <div class="popup2btns d-flex">
                            <a href="#" class="et-submit mx-3 confirm-link"   onclick="window.open('{{url('connect/twitch')}}','name','width=550,height=500,left=400,top=150,toolbar=0,status=0')"    data-bs-dismiss="modal" aria-label="Close">Confirm</a>
                            <a href="#" class="et-submit red-btn mx-3 confirm-link cancleBtn" data-bs-dismiss="modal" aria-label="Close">Cancel</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Modal -->
@php $dialogues = DB::table('dialogues')->where('name', 'facebookDialogue')->first(); @endphp
    <div class="modal fade influncer" id="facebookDialogue" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="facebookDialogueLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-body">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><img src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg" alt=""></button>
                    <div class="wizardHeading">{{$dialogues->heading}}</div>
                    <div class="wizardForm">
                       {!!$dialogues->content!!}
                        
                        <div class="popup2btns d-flex">
                            <a href="#" class="et-submit mx-3 confirm-link"   onclick="window.open('{{url('connect/facebook')}}','name','width=550,height=500,left=400,top=150,toolbar=0,status=0')"    data-bs-dismiss="modal" aria-label="Close">Confirm</a>
                            <a href="#" class="et-submit red-btn mx-3 confirm-link cancleBtn" data-bs-dismiss="modal" aria-label="Close">Cancel</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Modal -->
@php $dialogues = DB::table('dialogues')->where('name', 'instagramDialogue')->first(); @endphp
    <div class="modal fade influncer" id="instagramDialogue" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="instagramDialogueLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-body">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><img src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg" alt=""></button>
                    <div class="wizardHeading">Instagram Social-Connect</div>
                    <div class="wizardForm">
                        <h5 align="center" style="padding-bottom: 15px"><strong>Two steps must be completed before you can link your Instagram account with ClickItFame.</strong></h5>
                        <ul style="font-size: 18px !important;text-align:center;font-weight:normal;">
                          <li>1. Your Instagram-Profile must be a professional account (Creator or Business) <a href="https://help.instagram.com/***************/?helpref=uf_share" target="_blank"><br> View official Instagram-Guide</a></li>
                          <li>2. Your Instagram must be connected with a Facebook-Page <a href="https://help.instagram.com/***************" target="_blank"><br> View official Instagram-Guide</a></li>
                          
                        </ul>
                        <p style="font-size: 18px !important;text-align:center;">Now you can connect your Instagram-Account with ClickItFame. You need to log-in with the connected Facebook account. During this, you need to choose the connected 1.) Meta-Asset, 2.) Facebook-page, and 3.) the related Instagram-Profile you want to connect.</p>
                        <div class="popup2btns d-flex">
                            <a href="#" class="et-submit mx-3 confirm-link" onclick="window.open('{{ url('connect/instagram') }}','name','width=550,height=500,left=400,top=150,toolbar=0,status=0')" data-bs-dismiss="modal" aria-label="Close" style="min-width: 190px;">Confirm</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Modal -->
@php $dialogues = DB::table('dialogues')->where('name', 'tiktokDialogue')->first(); @endphp
    <div class="modal fade influncer" id="tiktokDialogue" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="tiktokDialogueLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-body">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><img src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg" alt=""></button>
                    <div class="wizardHeading">{{$dialogues->heading}}</div>
                    <div class="wizardForm">
                       {!!$dialogues->content!!}
                        
                        <div class="popup2btns d-flex">
                            <a href="#" class="et-submit mx-3 confirm-link"   onclick="window.open('{{url('connect/tiktok')}}','name','width=550,height=500,left=400,top=150,toolbar=0,status=0')"    data-bs-dismiss="modal" aria-label="Close">Confirm</a>
                            <a href="#" class="et-submit red-btn mx-3 confirm-link cancleBtn" data-bs-dismiss="modal" aria-label="Close">Cancel</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Modal -->
@php $dialogues = DB::table('dialogues')->where('name', 'snapchatDialogue')->first(); @endphp
    <div class="modal fade influncer" id="snapchatDialogue" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="snapchatDialogueLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-body">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><img src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg" alt=""></button>
                    <div class="wizardHeading">{{$dialogues->heading}}</div>
                    <div class="wizardForm">
                        {!!$dialogues->content!!}
                        
                        <div class="popup2btns d-flex">
                            <a href="#" class="et-submit mx-3 confirm-link"   onclick="window.open('{{url('connect/snapchat')}}','name','width=550,height=500,left=400,top=150,toolbar=0,status=0')"    data-bs-dismiss="modal" aria-label="Close">Confirm</a>
                            <a href="#" class="et-submit red-btn mx-3 confirm-link cancleBtn" data-bs-dismiss="modal" aria-label="Close">Cancel</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



<script>
    var count ;
       $.ajax({
                url: "{{url('get-social-connect')}}", 
            })
            .done(function(data) { 
                // console.log(data.social_connect); 
                if(data.social_connect == null){  
                count = 1;
                }else{ 
                count = 0; 
                }
                $(".advertisingMethod1").html(data.advertisingPage);
                $(".advertisingPriceMethod1").html(data.advertisingPricePage);
            });   

    function refreshParentError() {
        refreshParent();

        $("#errorMji").show();
        $(".closeErrorNow").click(function(){
            $("#errorMji").hide();
            {{ Session::forget('influencer_error') }} 
            {{Session::forget('influencer_follower_error')}}
        })
        // setTimeout(function () { 
        //     {{ Session::forget('influencer_error') }}
        //     {{Session::forget('influencer_follower_error')}}
        //     $("#errorMji").hide();
        // }, 4000); 
    }
    $(".closeErrorNow").click(function(){$(".errorMessage").hide()})
</script>