<h2 class="gnrlInformation">General Information</h2>
<input type="hidden" name="collection" value="1">
<div class="connectPrising ">   
    <div class="row">
        <div class="col-md-6">
            <div class="selectcatr">
                <label class="floatLabel">What is your influencer category? <span class="color-red">*</span></label>
                <div class="floating-label ">
                    <select class="form-control select" id="selectcategory1" name="category_id[]" aria-label="select category"  data-parsley-required-message="Please select influencer category." data-parsley-errors-container="#error_category" required> 
                        <option value="">Please select category</option>
                        @foreach($category as $row) 
                            <option 
                                @if($influencer_detail && isset($influencer_detail->category_id))  
                                    @foreach(explode(',',@$influencer_detail->category_id) as $cat) 
                                        @if($cat == $row->id) selected @endif 
                                    @endforeach
                                @endif
                                value="{{ $row->id }}">
                                {{ $row->name }}
                            </option>
                        @endforeach
                    </select>
                    <div class="required_message">Please select influencer category.</div>
                    <span id="error_category"></span>
                </div>
            </div>
        </div>
        <div class="col-md-12 reqr">
            <div class="form-group hashTagitle">
                <div class="row">
                    <div class="col-md-12">
                        <label>Following three #Hashtags describe your connect most <span class="color-red">*</span></label>
                    </div>
                    <input type="hidden" id="hashtag_arr" class="hashtag_arr" value="{{ implode(',',$hashtag_arr) }}">
                    <div class="col-sm-4 resdf">
                        <div class="floating-label smalSpace">
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <div class="input-group-text">#</div>
                                </div>
                                <input name='hashtags[]' id="hashtag1" class="form-control required inputtags aaa" value='@if(isset($hashtag[0])) {{$hashtag[0]->tags}} @endif' required>
                                <div class="required_message" id="">This value is required.</div>
                            </div>
                            <span id="tagsError"></span>
                        </div>
                    </div>
                    
                    <div class="col-sm-4 resdf">
                        <div class="floating-label smalSpace">
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <div class="input-group-text">#</div>
                                </div>
                                <input name='hashtags[]' id="hashtag2" class="form-control required inputtags bbb" value='@if(isset($hashtag[1])  ) {{$hashtag[1]->tags}} @endif' required>
                                <div class="required_message" id="">This value is required.</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4 resdf">
                        <div class="floating-label smalSpace">
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <div class="input-group-text">#</div>
                                </div>
                                <input name='hashtags[]' id="hashtag3" class="form-control required inputtags ccc" value='@if(isset($hashtag[2])) {{$hashtag[2]->tags}} @endif' data-parsley-notequalto="#hashtag1, #hashtag2" required>
                                <div class="required_message" id="">This value is required.</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="hashtagerror" style="display:none;">Please use different hashtag</div>
                    </div>
                </div>
            </div>
        </div>
        <hr />
        <div class="col-md-6">
            <div class="form-group">
                <label>What type of influencer are you? <span class="color-red">*</span></label>
                <div class="cusomRadio">
                    <div class="form-check ps-0">
                        <input class="form-check-input  influencer_type service-radio" type="radio" name="influencer_type" id="typeInfluencer1" value="Real personality" required data-parsley-required-message="Please select type of influencer." data-parsley-errors-container="#error_iay"  @if(isset($influencer_detail->influencer_type) && $influencer_detail->influencer_type == 'Real personality') checked  @endif  >
                        <label class="form-check-label" for="typeInfluencer1">
                            Real personality
                        </label>
                    </div>
                    <div class="form-check ps-0">
                        <input class="form-check-input influencer_type service-radio" type="radio" name="influencer_type" id="typeInfluencer2"   value="Content page" data-parsley-required-message="Please select type of influencer." @if(isset($influencer_detail->influencer_type) && $influencer_detail->influencer_type == 'Content page') checked @endif >
                        <label class="form-check-label" for="typeInfluencer2">
                            Content page
                        </label>
                    </div>
                </div>
                <div id="error_iay"></div>
            </div>                                        
        </div>
        <div class="col-md-6" id="viewgender"  @if(isset($influencer_detail->influencer_type) && $influencer_detail->influencer_type == 'Real personality') style="display: block;"   @else style="display: none;"  @endif  >
            <div class="form-group">
                <label>What is your gender? <span class="color-red">*</span></label>
                <div class="cusomRadio">
                    <div class="form-check ps-0">
                        <input class="form-check-input gender required service-radio" type="radio" name="gender" id="gender1" value="Male" @if(isset($influencer_detail->gender) && $influencer_detail->gender == 'Male') checked  @endif  data-parsley-errors-container="#error_ge" data-parsley-required-message="Please select gender." >
                        <label class="form-check-label" for="gender1">
                            Male
                        </label>
                        
                    </div>
                    <div class="form-check ps-0">
                        <input class="form-check-input service-radio" type="radio" name="gender" id="gender2" value="Female" @if(isset($influencer_detail->gender) && $influencer_detail->gender == 'Female') checked @endif data-parsley-required-message="Please select gender." >
                        <label class="form-check-label" for="gender2">
                            Female
                        </label>
                    </div>
                </div>
                <div id="error_ge"></div>
            </div>                                        
        </div>

    </div>
</div>
                        
<input type="hidden" name="publish" value="Publish"> 
<div class="step-nevigationbutton">
    <div class="nav-left back me-2" id="Back_step1">
        <img src="{{ asset('assets/front-end/images/icons/step-left.svg') }}" class="" alt="">
    </div>
    <div class="nav-right next ms-auto">
        <img src="{{ asset('assets/front-end/images/icons/step-right.svg') }}" class="" alt="">
    </div>
</div>



<script src="https://cdn.jsdelivr.net/npm/@yaireo/tagify"></script>
<script src="https://cdn.jsdelivr.net/npm/@yaireo/tagify/dist/tagify.polyfills.min.js"></script>
<link href="https://cdn.jsdelivr.net/npm/@yaireo/tagify/dist/tagify.css" rel="stylesheet" type="text/css" />
<script>
    $("input[name='influencer_type']").change(function(){
        if ($("input[type='radio'].influencer_type:checked").val() == 'Real personality') {
            $(".gender").attr('required','true');
            $("#viewgender").show();
        } else {
            $(".gender").removeAttr('required');
            $("#viewgender").hide();
        }
    });

    $(document).ready(function() {
        var input_tag_one = document.querySelector('#hashtag1');
        var arrayFromPHP = $("#hashtag_arr").val();

        // initialize Tagify on the above input node reference
        new Tagify(input_tag_one, {
            whitelist: arrayFromPHP.split(","), 
            maxTags: 1,
        })

        // The DOM element you wish to replace with Tagify
        var input_tag_two = document.querySelector('#hashtag2');

        // initialize Tagify on the above input node reference
        new Tagify(input_tag_two, {
            whitelist: arrayFromPHP.split(","),
            maxTags: 1,
        })


        // The DOM element you wish to replace with Tagify
        var input_tag_three = document.querySelector('#hashtag3');
        
        // initialize Tagify on the above input node reference
        new Tagify(input_tag_three, {
            whitelist: arrayFromPHP.split(","),
            maxTags: 1,
        
        });

        let allAreFilled = true;
        document.getElementById("draft1").querySelectorAll("[required]").forEach(function(i) {
            if (!allAreFilled) return;
            if (i.type === "radio") {
            let radioValueCheck = false;
            
            document.getElementById("draft1").querySelectorAll(`[name=${i.name}]`).forEach(function(r) {
                if (r.checked) radioValueCheck = true;
            })
            allAreFilled = radioValueCheck;
            return;
            }
            if (!i.value) { 
                allAreFilled = false;  return; 
            }
        });
        if (allAreFilled) {
            $('.ser_op#tablink1').removeClass("disable");
        }
    });

    $(document).click(function() {
        var nextlink = $(".nextLinkReq");
        if (!nextlink.is(event.target) && !nextlink.has(event.target).length && !nextlink.closest(".form-group").find(".form-control").is(event.target) && !nextlink.closest(".form-group").find(".form-control").has(event.target).length && !nextlink.closest(".form-group").find(".select2-container").is(event.target) && !nextlink.closest(".form-group").find(".select2-container").has(event.target).length ) {
            $(".form-control").css("border-color", "#EBEBFF");
            $(".nextLinkReq").hide();
            $(".nextLinkOnl").hide();
        }
    });
</script>