@extends('front-user.layouts.master_user_dashboard')

@section('content')
    <style>
        .campaign-header .campagin_info {
            width: 83%;
        }
    </style>
    <section id="campaignForm">
        <h1 class="section-heading"><span>Request History</span></h1>
        <div class="row">
            <div class="col-12 new_card_row">
                <div class="accordion" id="accordionHistory">
                    @if (count($history) > 0)
                        <?php $i = 1; ?>
                        @foreach ($history as $row)
                            @php
                            $closeIconsReasons = [
                                'Time expired' => 'Time expired',
                                'Cancelled' => 'Rejected',
                                'Cancelled By Customer' => 'Rejected By Customer',
                                'Cancelled By Admin' => 'Cancelled By Admin',
                                'Rejected' => 'Rejected',
                            ];
                            @endphp
                            {{-- Desktop View --}}
                            <div class="campaign-card desktop-view">
                                @include('front-user.pages.desktop.influencer.request-history')
                            </div>
                            {{-- Mobile View --}}
                            <div class="campaign-card mobile-view" style="display: none;">
                                @include('front-user.pages.mobile.influencer.request-history')
                            </div>

                            <div class="modal fade influncer request-popup wewPopup" id="requestForm{{ $row->id }}"
                                data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
                                aria-labelledby="requestFormLabel" aria-hidden="true">
                                <div class="modal-dialog default-width modal-dialog-centered">
                                    <div class="modal-content">
                                        <div class="modal-body">
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"
                                                aria-label="Close">
                                                <img src="{{ asset('/assets/front-end/images/icons/close-one.svg') }}"
                                                    alt="">
                                            </button>
                                            <div class="popup-title">{{ $row->compaign_title }}</div>
                                            <div class="popup-title-id">Campaign ID: {{ $row->compaign_id }}</div>
                                            <form method="post" id="requestFormSubmit{{ $row->id }}"
                                                action="{{ url('/request-form') }}" data-parsley-validate>
                                                @csrf
                                                <input type="hidden" name="influencer_request_detail_id"
                                                    id="influencer_request_detail_id"
                                                    value="{{ isset($row->id) ? $row->id : '' }}">
                                                <ul class="nav nav-tabs ordertab" id="myTab" role="tablist">
                                                    <li class="nav-item" role="presentation">
                                                        <button class="nav-link active"
                                                            id="general-information{{ $row->id }}-tab"
                                                            data-bs-toggle="tab"
                                                            data-bs-target="#general-information{{ $row->id }}"
                                                            type="button" role="tab"
                                                            aria-controls="general-information{{ $row->id }}"
                                                            aria-selected="true">General Information
                                                        </button>
                                                    </li>
                                                    <li class="nav-item" role="presentation">
                                                        <button class="nav-link" id="order-detail-tab{{ $row->id }}"
                                                            data-bs-toggle="tab"
                                                            data-bs-target="#order-detail{{ $row->id }}"
                                                            type="button" role="tab"
                                                            aria-controls="order-detail{{ $row->id }}"
                                                            aria-selected="false">My Tasks
                                                        </button>
                                                    </li>
                                                </ul>
                                                <div class="tab-content" id="myTabContent">
                                                    <div class="tab-pane fade show active"
                                                        id="general-information{{ $row->id }}" role="tabpanel"
                                                        aria-labelledby="general-information{{ $row->id }}-tab">
                                                        <div class="inside-table request-content">
                                                            @if (isset($row->user->company_name))
                                                                <div class="inside-table-row">
                                                                    <span class="type-label">Company Name</span>
                                                                    <span class="type-image"><img
                                                                            src="{{ asset('/assets/front-end/images/icons/icon-user-black.svg') }}"
                                                                            class="" alt=""></span>
                                                                    <span
                                                                        class="type-content">{{ $row->user->company_name }}</span>
                                                                </div>
                                                            @endif
                                                            <div class="inside-table-row">
                                                                <span class="type-label">Request date</span>
                                                                <span class="type-image"><img
                                                                        src="{{ asset('/assets/front-end/images/icons/icon-calender-black.svg') }}"
                                                                        class="" alt=""></span>
                                                                <span
                                                                    class="type-content">{{ date('d.m.Y', strtotime($row->created_at)) }}</span>
                                                            </div>
                                                            <div class="inside-table-row">
                                                                <span class="type-label">You get</span>
                                                                <span class="type-image"><img
                                                                        src="{{ asset('/assets/front-end/images/icons/req-money.svg') }}"
                                                                        class="" alt=""></span>
                                                                <span class="type-content">{{ number_format($row->current_price, 2) }} €</span>
                                                            </div>
                                                            <div class="inside-table-row">
                                                                <span class="type-label">Social Media</span>
                                                                <span class="type-image"><img
                                                                        src="{{ asset('/assets/front-end/images/icons/icon-cb-' . $row->media. '.svg') }}"
                                                                        class="" alt=""></span>
                                                                <span
                                                                    class="type-content">{{ ucfirst($row->media) }}</span>
                                                            </div>
                                                            <div class="inside-table-row">
                                                                <span class="type-label">Brand name</span>
                                                                <span class="type-image"><img
                                                                        src="{{ asset('/assets/front-end/images/icons/icon-brandname-black.svg') }}"
                                                                        class="" alt=""></span>
                                                                <span class="type-content">{{ $row->name }}</span>
                                                            </div>
                                                            <div class="inside-table-row">
                                                                <span class="type-label">Campaign type</span>
                                                                <span class="type-image"><img
                                                                        src="{{ asset('/assets/front-end/images/icons/icon-boostme-black.svg') }}"
                                                                        class="" alt=""></span>
                                                                <span class="type-content">{{ $row->post_type }}</span>
                                                            </div>
                                                            @if (isset($row->category))
                                                                <div class="inside-table-row">
                                                                    <span class="type-label">Category</span>
                                                                    <span class="type-image"><img
                                                                            src="{{ asset('/assets/front-end/images/icons/icon-category-black.svg') }}"
                                                                            class="" alt=""></span>
                                                                    <span
                                                                        class="type-content">{{ $row->category->name }}</span>
                                                                </div>
                                                            @endif
                                                            <div class="inside-table-row">
                                                                <span class="type-label">Results in</span>
                                                                <span class="type-image"><img
                                                                        src="{{ asset('/assets/front-end/images/icons/icon-clock-black.svg') }}"
                                                                        class="" alt=""></span>
                                                                <span class="type-content">@php
                                                                    $campaignRequestTime = App\Models\CampaignRequestTime::first();
                                                                    $time =
                                                                        isset($row->request_time_accept) &&
                                                                        $row->request_time_accept == 1
                                                                            ? $row->request_time + $row->time
                                                                            : $row->time;

                                                                    $created_date = date(
                                                                        'Y-m-d H:i:s',
                                                                        strtotime($row->created_at),
                                                                    );
                                                                    $updated_date = date(
                                                                        'Y-m-d H:i:s',
                                                                        strtotime($row->updated_at),
                                                                    );
                                                                    $campaignDate = date(
                                                                        'Y-m-d H:i:s',
                                                                        strtotime(
                                                                            $created_date .
                                                                                ' + ' .
                                                                                $campaignRequestTime->request_time .
                                                                                ' days',
                                                                        ),
                                                                    );
                                                                    $date = date('Y-m-d H:i:s');
                                                                    $seconds =
                                                                        strtotime($campaignDate) - strtotime($date);

                                                                    $days = floor($seconds / 86400);
                                                                    if ($days < 3 && $days >= 0) {
                                                                        $hours = floor(
                                                                            ($seconds - $days * 86400) / 3600,
                                                                        );

                                                                        $minutes = floor(
                                                                            ($seconds - $days * 86400 - $hours * 3600) /
                                                                                60,
                                                                        );

                                                                        $seconds = floor(
                                                                            $seconds -
                                                                                $days * 86400 -
                                                                                $hours * 3600 -
                                                                                $minutes * 60,
                                                                        );
                                                                    }
                                                                @endphp
                                                                    {{-- <div class="timing" id="timer1{{ $row->compaign_id }}"></div> --}}
                                                                    <div class="timing">10 days</div>
                                                                </span>
                                                            </div>
                                                        </div>

                                                    </div>
                                                    <div class="tab-pane fade" id="order-detail{{ $row->id }}"
                                                        role="tabpanel"
                                                        aria-labelledby="order-detail-tab{{ $row->id }}">
                                                        <div class="request-content-data icon-before">
                                                            @php $tasks = $row->tasks ; @endphp
                                                            @if (isset($tasks))
                                                                @foreach ($tasks as $task)
                                                                    @if (isset($task->taskDetail) && $task->type == 'SocialMediaName')
                                                                        <div class="inside-table-row">
                                                                            <div class="order-titles">
                                                                                {{ $task->taskDetail->task }}
                                                                            </div>
                                                                            <div class="order-content">
                                                                                {{ $task->value }}
                                                                            </div>
                                                                        </div>
                                                                    @endif
                                                                @endforeach


                                                                @foreach ($tasks as $task)
                                                                    @if (isset($task->taskDetail) && $task->type == 'Link')
                                                                        <div class="inside-table-row">
                                                                            <div class="order-titles">
                                                                                {{ $task->taskDetail->task }}
                                                                            </div>
                                                                            <div class="order-content">
                                                                                <div class="order-link">
                                                                                    <div class="link"
                                                                                        id="myInput{{ $task->id }}">
                                                                                        {{ $task->value }}</div>
                                                                                    <div class="copy-link">
                                                                                        <a class="copy_text"
                                                                                            id="jjhu"
                                                                                            data-toggle="tooltip"
                                                                                            title="Copy to Clipboard"
                                                                                            href="{{ $task->value }}">
                                                                                            <span class="">COPY</span>
                                                                                        </a>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    @endif
                                                                @endforeach


                                                                @foreach ($tasks as $task)
                                                                    @if (isset($task->taskDetail) && $task->type == 'UploadContent')
                                                                        <div class="inside-table-row">
                                                                            <div class="order-titles">
                                                                                {{ $task->taskDetail->task }}
                                                                            </div>
                                                                            <div class="order-content">

                                                                                <a class="table-btn"
                                                                                    href="{{ asset('storage/' . $task->value) }}"
                                                                                    download
                                                                                    style="color: black !important;width:186px !important;height:40px;box-shadow:none !important;">
                                                                                    Download
                                                                                </a>
                                                                                @if ($row->post_content_type == 'video')
                                                                                    <img src="{{ url('/assets/front-end/icons/video_placeholder.png') }}" width="40">
                                                                                @else
                                                                                    <img src="{{ url('/assets/front-end/icons/image_placholder.png') }}" width="40">
                                                                                @endif
                                                                            </div>
                                                                        </div>
                                                                    @endif
                                                                @endforeach

                                                                @foreach ($tasks as $task)
                                                                    @if (isset($task->taskDetail) && $task->type == 'Hashtag')
                                                                        <div class="inside-table-row">
                                                                            <div class="order-titles">
                                                                                {{ $task->taskDetail->task }}
                                                                            </div>
                                                                            <div class="order-content">
                                                                                <?php $tags = explode(',', $task->value); ?>
                                                                                @foreach ($tags as $tag)
                                                                                    @if ($tag)
                                                                                        <div class="order-hash-tag">
                                                                                            <img src="{{ asset('/assets/front-end/images/icon-hash.png') }}" alt="">
                                                                                            {{ $tag }}
                                                                                        </div>
                                                                                    @endif
                                                                                @endforeach

                                                                            </div>
                                                                        </div>
                                                                    @endif
                                                                @endforeach

                                                                @foreach ($tasks as $task)
                                                                    @if (isset($task->taskDetail) && $task->type == 'Info')
                                                                        <div class="inside-table-row">
                                                                            <div class="order-titles">
                                                                                {{ $task->taskDetail->task }}
                                                                            </div>
                                                                        </div>
                                                                    @endif
                                                                @endforeach
                                                            @endif
                                                        </div>

                                                    </div>
                                                </div>
                                                <!-- Selected users //-->

                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php $i++; ?>
                        @endforeach
                    @else
                        <div class="no-data-div">
                            <img src="{{ asset('/assets/front-end/images/icons/icon-no-data-image.png') }}"
                                alt="">
                            <div class="no-data-contant">
                                Sorry, you have no request history at the moment.
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </section>

@endsection

@section('script_links')
    <script type="text/javascript" src="https://cdn.datatables.net/v/bs5/dt-1.13.1/datatables.min.js"></script>
    <script type="text/javascript" src="{{ URL::asset('/assets/front-end/js/loadMoreResults.js') }}"></script>
@endsection

@section('script_codes')
    <script>
        $(document).ready(function() {
            // $('#venueTable').DataTable();
        });
        $(document).ready(function() {
            $('#accordionHistory').loadMoreResults({
                displayedItems: 5,
                showItems: 5,
                button: {
                    'class': 'btn-load-more',
                    'text': 'Load More'
                }
            });
        })
    </script>
@endsection
