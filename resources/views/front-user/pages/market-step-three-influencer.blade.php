<div class="influncer-filter filter-button-outer">
    <button type="button" class="button-open-filter">
        (0) Filter <img src="{{ asset('/assets/front-end/images/icons/filter-icon.svg') }}" class=""
            alt="">
    </button>
    <img src="{{ asset('/assets/front-end/images/icons/new_social_media_' . $formData['mp_socialmedia']. '.png') }}"
        class="" alt="">
</div>
<div class="influncer-filter filter-box-outer">
    <div class="filter-div">
        <div class="filter-buttons">
            <button type="button" class="button-close-filter">
                (0) Filter <img src="{{ asset('/assets/front-end/images/icons/filter-icon.svg') }}" class=""
                    alt="">
            </button>
            <select class="filter-by sort_by checkFilter" name="sort_by" id="sort_by">
                <option value="">Sort by</option>
                <option value="followers-DESC" @if (isset($formData['sort_by']) && $formData['sort_by'] == 'followers-DESC') selected @endif>Follower High to low
                </option>
                <option value="followers-ASC" @if (isset($formData['sort_by']) && $formData['sort_by'] == 'followers-ASC') selected @endif>Follower Low to High
                </option>
                <option value="price-DESC" @if (isset($formData['sort_by']) && $formData['sort_by'] == 'price-DESC') selected @endif>Price High to low</option>
                <option value="price-ASC" @if (isset($formData['sort_by']) && $formData['sort_by'] == 'price-ASC') selected @endif>Price Low to High</option>
                <!-- <option value="rating-DESC" @if (isset($formData['sort_by']) && $formData['sort_by'] == 'price-DESC') selected @endif >Rating High to low</option> -->
            </select>
            <button type="button" class="button-reset-filter resetFilter">
                Reset <img src="{{ asset('/assets/front-end/images/icons/icon-reset-filter.svg') }}" class=""
                    alt="">
            </button>
        </div>
        <div class="filter-form">
            <div class="form-group">
                <label>Target Age</label>
                <select class="select form-control checkFilter" name="target_age" id="target_age">
                    <option value="">Select</option>
                    @php $ages = []; @endphp
                    @if (isset($influencerDetails))
                        @foreach ($influencerDetails as $row)
                            @php  array_push($ages,$row->ages);@endphp
                        @endforeach
                    @endif
                    @php  $ages= array_unique($ages); @endphp
                    @foreach ($ages as $row)
                        <option @if (isset($formData['target_age']) && $formData['target_age'] == $row) selected @endif>{{ $row }}</option>
                    @endforeach
                </select>
            </div>
            <div class="form-group">
                <label>Target Gender</label>
                <select class="select form-control checkFilter" name="content_attracts" id="target_gender">
                    <option value="">Select</option>
                    @php $content_attracts = []; @endphp
                    @if (isset($influencerDetails))
                        @foreach ($influencerDetails as $row)
                            @php  array_push($content_attracts,$row->content_attracts);@endphp
                        @endforeach
                    @endif
                    @php  $content_attracts = array_unique($content_attracts); @endphp
                    @foreach ($content_attracts as $row)
                        @if ($row != '')
                            <option @if (isset($formData['content_attracts']) && $formData['content_attracts'] == $row) selected @endif>{{ $row }}</option>
                        @endif
                    @endforeach
                </select>
            </div>
            <div class="form-group">
                <label>Influencer type</label>
                <select class="select form-control checkFilter" name="influencer_type" id="influencer_type">
                    <option value="">Select</option>
                    @php $influencer_type = []; @endphp
                    @if (isset($influencerDetails))
                        @foreach ($influencerDetails as $row)
                            @php  array_push($influencer_type,$row->influencer_type);@endphp
                        @endforeach
                    @endif
                    @php  $influencer_type= array_unique($influencer_type); @endphp
                    @foreach ($influencer_type as $row)
                        @if ($row != '')
                            <option @if (isset($formData['influencer_type']) && $formData['influencer_type'] == $row) selected @endif>{{ $row }}</option>
                        @endif
                    @endforeach
                </select>
            </div>
            <div class="form-group">
                <label>Minimum Followers</label>
                <select name="followers" class="form-control checkFilter" id="minimum-followers">
                    <option value="">Select</option>
                    <option @if (isset($formData['followers']) && $formData['followers'] == '4') Selected @endif value="4">5</option>
                    <option @if (isset($formData['followers']) && $formData['followers'] == '500') Selected @endif value="500">500</option>
                    <option @if (isset($formData['followers']) && $formData['followers'] == '1000') Selected @endif value="1000">1000</option>
                    <option @if (isset($formData['followers']) && $formData['followers'] == '10000') Selected @endif value="10000">10000</option>
                    <option @if (isset($formData['followers']) && $formData['followers'] == '50000') Selected @endif value="50000">50000</option>
                    <option @if (isset($formData['followers']) && $formData['followers'] == '100000') Selected @endif value="100000">100000</option>
                    <option @if (isset($formData['followers']) && $formData['followers'] == '500000') Selected @endif value="500000">500000</option>
                    <option @if (isset($formData['followers']) && $formData['followers'] == '1000000') Selected @endif value="1000000">1000000+</option>
                </select>
            </div>
            <div class="form-group">
                <label>Rank</label>
                <select class="form-control checkFilter" name="rank" id="rank">
                    <option value="">Select</option>

                    @php $pricing = []; @endphp
                    @if (isset($influencerDetails))
                        @foreach ($influencerDetails as $row)
                            @php  array_push($pricing,$row->pricing);@endphp
                        @endforeach
                    @endif
                    @php  $pricing= array_unique($pricing); @endphp
                    @foreach ($pricing as $row)
                        @if ($row != '')
                            <option @if (isset($formData['rank']) && $formData['rank'] == $row) selected @endif>{{ $row }}</option>
                        @endif
                    @endforeach




                </select>
            </div>
            <div class="form-group">
                <label>Target Language</label>
                <select class="select form-control checkFilter" name="language" id="language">
                    <option value="">Select</option>
                    @php $language = []; @endphp
                    @if (isset($influencerDetails))
                        @foreach ($influencerDetails as $row)
                            @php  array_push($language,$row->content_language);@endphp
                        @endforeach
                    @endif
                    @php  $language= array_unique($language); @endphp
                    @foreach ($language as $row)
                        @if ($row != '')
                            <option @if (isset($formData['language']) && $formData['language'] == $row) selected @endif>{{ $row }}</option>
                        @endif
                    @endforeach
                </select>
            </div>
            <div class="form-group">
                <label>Influencer category</label>
                <select class="form-control  checkFilter" id="selectCategory" name="category_id[]" multiple>
                    @if ($category != '')
                        @foreach ($category as $row)
                            <option value="{{ $row->id }}">{{ $row->name }}</option>
                        @endforeach
                    @endif
                </select>
            </div>
            <div class="form-group">
                <label>Influencer hashtag</label>
                <select class="select form-control multiple floating-input checkFilter" multiple="multiple"
                    name="hashtag[]" id="hashtags">

                    @php $hashtags = []; @endphp
                    @if (isset($influencerDetails))
                        @foreach ($influencerDetails as $row)
                            @if (isset($row->tags))
                                @foreach ($row->tags as $tags)
                                    @php  array_push($hashtags,$tags->tags);@endphp
                                @endforeach
                            @endif
                        @endforeach
                    @endif
                    @php  $hashtags= array_unique($hashtags); @endphp
                    @if ($hashtags != '')
                        @foreach ($hashtags as $row)
                            @if ($row != '')
                                <option @if (isset($formData['hashtags']) && in_array($row, $formData['hashtags'])) selected @endif>{{ $row }}
                                </option>
                            @endif
                        @endforeach
                    @endif

                </select>
            </div>
            <div class="form-group">
                <label>Influencer gender</label>
                <select class="select form-control checkFilter" name="gender" id="gender">
                    <option value="">Select</option>
                    @php $gender = []; @endphp
                    @if (isset($influencerDetails))
                        @foreach ($influencerDetails as $row)
                            @php  array_push($gender,$row->gender);@endphp
                        @endforeach
                    @endif
                    @php  $gender= array_unique($gender); @endphp
                    @foreach ($gender as $row)
                        @if ($row != '')
                            <option @if (isset($formData['gender']) && $formData['gender'] == $row) selected @endif>{{ $row }}</option>
                        @endif
                    @endforeach
                </select>
            </div>
            <div class="form-group">
                <label>Price (maximum)</label>
                <input type="text" name="price" class="checkFilter" id="amount-price"
                    value="{{ isset($formData['price']) ? $formData['price'] : 0 }}"
                    oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');">
            </div>
        </div>
    </div>
    <img src="{{ asset('/assets/front-end/images/icons/new_social_media_' . $formData['mp_socialmedia'] . '.png') }}" alt="">
</div>

<div class="influncer-list">
    <style>
        .card-container {
            position: relative;
            background: white;
            width: 250px;
            padding: 8px;
            border-radius: 15px;
            box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .card-container::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 35px;
            background: #AD80FF;
            border-bottom-left-radius: 15px;
            border-bottom-right-radius: 15px;
            z-index: 1;
        }

        .custom-card {
            width: 100%;
            background-color: #fff;
            border-radius: 15px;
            position: relative;
            font-family: 'Outfit';
            z-index: 2;
        }

        .star-icon {
            font-size: 1.5rem;
            color: #AD80FF;
        }

        .profile-pic {
            width: 80px;
            height: 80px;
            border: 3px solid #AD80FF;
            padding: 3px;
            border-radius: 50%;
            overflow: hidden;
        }

        .profile-pic img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        h3 {
            font-size: 1em;
        }

        .view-profile {
            color: #AD80FF;
            font-size: 0.8em;
        }

        .followers {
            font-size: 1em;
            font-weight: 700;
            margin-bottom: 0.3em;
        }

        .price {
            font-size: 2em !important;
            color: #000;
            font-weight: 700;
        }

        .country {
            font-size: 1.2em;
        }

        .select-btn {
            font-size: 0.7em;
            color: #AD80FF;
            border: 1px solid #AD80FF;
            border-radius: 20px;
            transition: background-color 0.3s;
            padding: .275rem 3rem;
        }

        .select-btn:hover {
            background-color: #fff;
        }

        .influncer-detail.selected.card-container {
            background-color: #AD80FF;
        }

        .influncer-detail.selected.card-container::after {
            background-color: #fff;
        }

        .influncer-detail.selected .custom-card {
            background-color: #AD80FF;
        }

        .influncer-detail.selected .profile-pic {
            border: 3px solid white;
        }

        .influncer-detail.selected h3 {
            color: white;
        }

        .influncer-detail.selected .view-profile {
            color: white;
        }

        .influncer-detail.selected .followers {
            color: white !important;
        }

        .influncer-detail.selected .price {
            color: white !important;
        }

        .influncer-detail.selected .select-btn {
            color: black !important;
            background-color: white;
        }
    </style>
    @if (isset($influencerDetails))
        @foreach ($influencerDetails as $row)
            @php
                $req_count = App\Models\InfluencerRequestDetail::where('influencer_detail_id', $row->i_id)
                    ->where(function ($query) {
                        $query->where('review', '=', null)->orWhere('review', '=', 0);
                    })
                    ->where('refund_reason', null)
                    ->where('finish', null)
                    ->count();
                $user = App\Models\User::where('id', $row->user_id)->first();
            @endphp

            @if ($req_count < 5 && (isset($user) && $user->status == 1))
                @php
                    $media = \App\Models\SocialConnect::where('user_id', $row->user_id)
                        ->where('media', $row->media)
                        ->first();
                    $userData = \App\Models\User::where('id', $row->user_id)->first();
                    if (
                        $userData != null &&
                        $userData->count() > 0 &&
                        ($userData->trophy != '' && $userData->trophy != null)
                    ) {
                        $trophy = $userData->trophy;
                    } else {
                        $trophy = 'Bronze';
                    }
                @endphp
                @if (isset($media->picture))
                    <div class="influncer-detail card-container text-center mx-auto mt-4"
                        data-id="inflinser_id{{ $row->i_id }}">
                        <div class="influencer-content card custom-card text-center p-0 mx-auto my-0"
                            style="box-shadow: none; border:none;">
                            <div class="star-icon position-absolute top-0 start-0 mx-2 my-1">
                                <img id="card_clickitfame_logo_inflinser_id{{ $row->i_id }}"
                                    src="{{ asset('/assets/front-end/images/icons/clickitfame_logo.svg') }}"
                                    width="25" height="25" alt="">
                            </div>
                            <div class="profile-pic mx-auto">
                                <img src="{{ asset('storage/' . $media->picture) }}" alt="Profile Picture"
                                    class="rounded-circle">
                            </div>
                            <h3 class="mt-3 mb-0 user_name" style="font-size: 1.3em; font-weight: 600;">
                                {{ '@' . $row->username }}</h3>

                            {{-- hidden is_small _business_owner check for payment details page --}}
                            <span style="display: none;" id="is_small_business_owner">{{$userData->is_small_business_owner}}</span>

                            <span href="#" data-bs-toggle="modal"
                                data-bs-target="#influncerdetailpopup{{ $row->id }}"
                                class="view-profile text-decoration-underline d-block mb-2" style="cursor: pointer;">View
                                Profile</span>
                            <p class="followers">
                                <span class="follower-count-val" data-followers="{{ $row->followers }}">{{ $row->followers }}</span> Followers
                            </p>
                            <p class="price fs-1 mb-0">
                                € <span class="user_price-val" data-price="{{ $row->type_price }}">{{ number_format($row->type_price, 2) }}</span>
                            </p>
                            <p class="mb-0">
                                <img src="{{ asset('/assets/front-end/images/icons/germany-straight-flag.svg') }}" width="20" height="20" alt="">
                                <img src="{{ asset('/assets/front-end/images/icons/trofy-' . $trophy . '.svg') }}" width="20" height="20" alt="">
                            </p>
                            <div class="mb-3">
                                <button type="button" class="select-button btn select-btn my-3">SELECT</button>
                            </div>

                        </div>

                        <!--Influncer profile popup Start -->
                        <div class="modal fade influncerdetailpopup" id="influncerdetailpopup{{ $row->id }}"
                            data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
                            aria-labelledby="payoutconfirmpopupLabel" aria-hidden="true">
                            @php
                                $fullInfluencerDetail = \App\Models\InfluencerDetail::where(
                                    'user_id',
                                    $row->user_id,
                                )->first();
                                $hashTags = \App\Models\Hashtag::where('user_id', $row->user_id)->get();
                                $userData = \App\Models\User::where('id', $row->user_id)->first();
                                if ($userData->trophy == '') {
                                    $userData->trophy = 'Bronze';
                                }

                                $influencerCategory = \App\Models\Category::where(
                                    'id',
                                    $fullInfluencerDetail->category_id,
                                )->first();
                                $country = \App\Models\Country::where('id', $userData->country)->first();
                            @endphp
                            <div class="modal-dialog modal-dialog-centered modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header" style="background: #AD80FF;">
                                        <button class="close-popup-button" type="button"
                                            data-bs-dismiss="modal"><img
                                                src="{{ asset('/assets/front-end/images/icons/close-one.svg') }}"
                                                alt=""></button>
                                        <div class="modal-header-outer mb-3">
                                            <div class="modal-header-content">
                                                <div class="header-influncer-image">
                                                    <img src="{{ asset('storage/' . $media->picture) }}" alt="">
                                                </div>
                                                <div class="header-influncer-flage">
                                                    <img src="{{ asset('/assets/front-end/images/icons/icon-flage.svg') }}" alt="">
                                                </div>
                                                <div class="header-influncer-trophy">
                                                    <img src="{{ asset('/assets/front-end/images/icons/trofy-' . $trophy . '.svg') }}" alt="">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-body">

                                        <div class="modal-header-username d-flex justify-content-center mt-4"
                                            style="color: black; font-size: 22px;">
                                            {{ $row->username }}
                                        </div>
                                        <div class="d-flex justify-content-center mt-0"
                                            style="color: black; font-size: 15px;font-family: Mulish; opacity: 0.7;">
                                            {{ $fullInfluencerDetail->influencer_type }} |
                                            {{ $influencerCategory->name }}
                                        </div>
                                        <div class="row justify-content-center mt-3"
                                            style="color: black; font-size: 15px;font-family: Mulish;">
                                            <span class="col-md-3 text-center">Age of the Follower:
                                                {{ $fullInfluencerDetail->ages }}</span>
                                            <span class="col-md-3 text-center">Country:
                                                {{ $country->name }}</span>
                                            <span class="col-md-3 text-center">Content Language:
                                                {{ $fullInfluencerDetail->content_language }}</span>
                                        </div>
                                        <div class="row justify-content-center mt-4">
                                            @foreach ($hashTags as $hashTag)
                                                <div class="col-md-2 text-center mx-3 my-2"
                                                    style="color: #AD80FF; border: 1px solid #AD80FF; padding: 0.375em 0.85em; border-radius: 25px;">
                                                    #{{ $hashTag->tags }}
                                                </div>
                                            @endforeach
                                        </div>
                                        {{-- <div class="popup-hashtag d-flex justify-content-center">
                                            @if (isset($row->tags))
                                                @foreach ($row->tags as $tags)
                                                    <span>#{{ $tags->tags }}</span>
                                                @endforeach
                                            @endif
                                        </div> --}}
                                        <div class="popup-social-section d-flex justify-content-center"
                                            style="padding: 25px 0;">

                                            @php $medias =  \App\Models\SocialConnect::where('user_id',$row->user_id)->get(); @endphp
                                            @foreach ($medias as $socials)
                                                <div
                                                    class="popup-social-image @if ($socials->media == $row->media) green-circle @endif ">
                                                    <a href="{{ $socials->url }}" target="_blank">
                                                        <img src="{{ asset('/') }}/assets/front-end/images/icons/new_social_media_{{ $socials->media }}.png"
                                                            alt="">
                                                        <span>{{ $socials->followers }} Follower</span>
                                                    </a>
                                                </div>
                                            @endforeach
                                        </div>
                                        <div class="latest-campaigns">
                                            <div class="latest-campaigns-title">Latest Campaigns</div>
                                            <div class="latest-campaigns-box-outer">



                                                @php
                                                    $influencerData = \App\Models\InfluencerRequestDetail::join(
                                                        'influencer_details',
                                                        'influencer_request_details.influencer_detail_id',
                                                        '=',
                                                        'influencer_details.id',
                                                    )
                                                        ->leftjoin(
                                                            'influencer_request_accepts',
                                                            'influencer_request_accepts.influencer_request_detail_id',
                                                            '=',
                                                            'influencer_request_details.id',
                                                        )
                                                        ->leftjoin(
                                                            'rating_reviews',
                                                            'influencer_request_accepts.id',
                                                            '=',
                                                            'rating_reviews.influencer_request_accept_id',
                                                        )
                                                        ->leftjoin(
                                                            'users',
                                                            'users.id',
                                                            '=',
                                                            'influencer_details.user_id',
                                                        )
                                                        ->select(
                                                            'influencer_request_details.*',
                                                            'users.first_name',
                                                            'users.last_name',
                                                            'rating_reviews.rating',
                                                            'rating_reviews.review',
                                                        )
                                                        ->where(
                                                            'influencer_request_details.influencer_detail_id',
                                                            $row->i_id,
                                                        )
                                                        ->orderBy('influencer_request_details.id', 'desc')
                                                        ->get()
                                                        ->take(3);
                                                @endphp

                                                @if (isset($influencerData))
                                                    @foreach ($influencerData as $data)
                                                        <div class="latest-campaigns-box">
                                                            <div class="latest-campaigns-name">
                                                                {{ $data->first_name }} {{ $data->last_name }}
                                                            </div>
                                                            <div class="latest-campaigns-date">
                                                                {{ date('d-m-Y', strtotime($data->created_at)) }}
                                                            </div>
                                                            <div class="latest-campaigns-stars">
                                                                @if ($data->rating != '')
                                                                    @if ($data->rating == 0)
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png"
                                                                            alt="">
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png"
                                                                            alt="">
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png"
                                                                            alt="">
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png"
                                                                            alt="">
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png"
                                                                            alt="">
                                                                    @endif
                                                                    @if ($data->rating == 1)
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png"
                                                                            alt="">
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png"
                                                                            alt="">
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png"
                                                                            alt="">
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png"
                                                                            alt="">
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png"
                                                                            alt="">
                                                                    @endif
                                                                    @if ($data->rating == 2)
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png"
                                                                            alt="">
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png"
                                                                            alt="">
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png"
                                                                            alt="">
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png"
                                                                            alt="">
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png"
                                                                            alt="">
                                                                    @endif
                                                                    @if ($data->rating == 3)
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png"
                                                                            alt="">
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png"
                                                                            alt="">
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png"
                                                                            alt="">
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png"
                                                                            alt="">
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png"
                                                                            alt="">
                                                                    @endif
                                                                    @if ($data->rating == 4)
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png"
                                                                            alt="">
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png"
                                                                            alt="">
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png"
                                                                            alt="">
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png"
                                                                            alt="">
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png"
                                                                            alt="">
                                                                    @endif
                                                                    @if ($data->rating == 5)
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png"
                                                                            alt="">
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png"
                                                                            alt="">
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png"
                                                                            alt="">
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png"
                                                                            alt="">
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png"
                                                                            alt="">
                                                                    @endif
                                                                @endif
                                                            </div>
                                                            <div class="latest-campaigns-text">
                                                                @if ($data->review != '')
                                                                    “{{ $data->review }}”
                                                                @else
                                                                    No Review
                                                                @endif
                                                            </div>
                                                        </div>
                                                    @endforeach
                                                @else
                                                    <div class="nodata-text">
                                                        The influencer has not yet run a campaign. Take the
                                                        opportunity
                                                        and be the first to launch a successful campaign with this
                                                        influencer.
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--Influncer profile popup end-->
                    </div>
                @endif
            @endif
        @endforeach
    @endif

</div>
