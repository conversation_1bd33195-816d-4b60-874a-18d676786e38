
<div class="tab-pane fade show active" id="vodTab" role="tabpanel" aria-labelledby="vodTab-tab">

        <div class="final-step-outer">
            <div class="final-step">
                <img src="{{ asset('/') }}/assets/front-end/images/icons/image-party-popper.svg" class="icon" alt="" >
                You are all set
            </div>
        </div>
@php
 $social_connect_high =   App\Models\SocialConnect::where('user_id',Auth::id())->orderByRaw('CONVERT(followers, SIGNED) desc')->first()
@endphp
   {{-- <div class="tabsram">
        <div class="tabsram-in">
            <div class="orcTitle">
                Your marketplace appearance
            </div>
            @if(isset(Auth::user()->advertisingMethodPrice) ) 
            <div class="orcCont">
                @if(isset(Auth::user()->advertisingMethodPrice->sharecontent) &&  Auth::user()->advertisingMethodPrice->sharecontent== 1 && Auth::user()->advertisingMethodPrice->sharecontent_media!= '' ) 
                @php
 
                if(Auth::user()->advertisingMethodPrice->sharecontent_media == 'facebook')
                {
                     $media = $social_connect_facebook ; 
                }elseif(Auth::user()->advertisingMethodPrice->sharecontent_media == 'instagram')
                {
                     $media = $social_connect_instagram ; 
                }elseif(Auth::user()->advertisingMethodPrice->sharecontent_media == 'twitter')
                {
                     $media = $social_connect_twitter ; 
                }elseif(Auth::user()->advertisingMethodPrice->sharecontent_media == 'youtube')
                {
                     $media = $social_connect_youtube ; 
                }elseif(Auth::user()->advertisingMethodPrice->sharecontent_media == 'twitch')
                {
                     $media = $social_connect_twitch ; 
                }elseif(Auth::user()->advertisingMethodPrice->sharecontent_media  == 'tiktok')
                {
                     $media = $social_connect_tiktok ; 
                }elseif(Auth::user()->advertisingMethodPrice->sharecontent_media == 'snapchat')
                {
                     $media = $social_connect_snapchat ; 
                }

                @endphp 
                <div class="flexUser data_7 {{Auth::user()->advertisingMethodPrice->sharecontent_media}}" style="display: block;">
                    <div class="oii">Share Content</div>
                    <div class="userDetails newClass{{Auth::user()->id}}" id="{{Auth::user()->id}}">  
                        <div class="userDetails1 d-flex">
                            <a href="{{ isset($media)?$media->url:''}}" target="_blank">
                                <span class="handelname">{{isset($media)?$media->name:''}}  {{Auth::user()->request }}</span> 
                            </a>
                            <span class="handelpletform"> 
                                <img src="{{ asset('/') }}/assets/front-end/images/icons/campaigns-{{Auth::user()->advertisingMethodPrice->sharecontent_media}}.png"  class="small-icon" alt=""> 
                            </span>
                        </div>                        
                        <div class="userDetails2">
                            <div class="userDetailImage">
                                <a href="{{ isset($media)?$media->url:''}}" target="_blank"> 
                                    <img src="{{asset('storage/'.(isset($media)?$media->picture:''))}}" class="iconColored" alt="">
                                </a>
                            </div>
                            <div class="userDetailInfo">
                                <span class="infoName">{{isset($media)?$media->followers:''}} Follower</span>
                                <span class="infoStar">
                                    <i class="fa-solid fa-star"></i>
                                    <i class="fa-solid fa-star"></i>
                                    <i class="fa-solid fa-star"></i>
                                    <i class="fa-regular fa-star"></i>
                                    <i class="fa-regular fa-star"></i>
                                </span>
                            </div>
                        </div>
                        <div class="userDetails3">
                            <div class="targetHeading">Target groups</div>
                            <div class="targetgroups d-flex flex-wrap">
                                <div class="targetgroup">Age <br><strong class="ages"></strong></div>
                                <div class="targetgroup">Language<br><strong class="content_language"></strong></div>
                                <div class="targetgroup">Gender<br><strong class="content_attracts"></strong></div>
                            </div>
                            <div class="targetgroup forTag"> 
                                @foreach(Auth::user()->hashtags as $tags)
                                <span >#{{$tags->tags}}</span> 
                                @endforeach
                            </div>
                        </div>
                        <div class="userDetails4">
                            <span class="infoPrice_{{Auth::user()->advertisingMethodPrice->sharecontent_media}}">€ {{Auth::user()->advertisingMethodPrice->sharecontent_price}}</span> 
                        </div>                             
                    </div>
                </div>
                @endif



                @if(isset(Auth::user()->advertisingMethodPrice->video) &&  Auth::user()->advertisingMethodPrice->video== 1 && Auth::user()->advertisingMethodPrice->video_media!= ''  ) 
                @php
 
                if(Auth::user()->advertisingMethodPrice->video_media == 'facebook')
                {
                     $media = $social_connect_facebook ; 
                }elseif(Auth::user()->advertisingMethodPrice->video_media == 'instagram')
                {
                     $media = $social_connect_instagram ; 
                }elseif(Auth::user()->advertisingMethodPrice->video_media == 'twitter')
                {
                     $media = $social_connect_twitter ; 
                }elseif(Auth::user()->advertisingMethodPrice->video_media == 'youtube')
                {
                     $media = $social_connect_youtube ; 
                }elseif(Auth::user()->advertisingMethodPrice->video_media == 'twitch')
                {
                     $media = $social_connect_twitch ; 
                }elseif(Auth::user()->advertisingMethodPrice->video_media  == 'tiktok')
                {
                     $media = $social_connect_tiktok ; 
                }elseif(Auth::user()->advertisingMethodPrice->video_media == 'snapchat')
                {
                     $media = $social_connect_snapchat ; 
                }

                @endphp
                <div class="flexUser data_7 {{Auth::user()->advertisingMethodPrice->video_media}}" style="display: block;">
                    <div class="oii">Video on Demand</div>
                    <div class="userDetails newClass{{Auth::user()->id}}" id="{{Auth::user()->id}}">  
                        <div class="userDetails1 d-flex">
                            <a href="{{ isset($media)?$media->url:''}}" target="_blank">
                                <span class="handelname">{{isset($media)?$media->name:''}}   {{Auth::user()->request }}</span> 
                            </a>
                            <span class="handelpletform">
                                <img src="{{ asset('/') }}/assets/front-end/images/icons/campaigns-{{Auth::user()->advertisingMethodPrice->video_media}}.png" class="small-icon" alt=""> 
                            </span>
                        </div>                        
                        <div class="userDetails2">
                            <div class="userDetailImage">
                                <a href="{{ isset($media)?$media->url:''}}" target="_blank"> 
                                    <img src="{{asset('storage/'.(isset($media)?$media->picture:''))}}" class="iconColored" alt="">
                                </a>
                            </div>
                            <div class="userDetailInfo">
                                <span class="infoName">{{isset($media)?$media->followers:''}} Follower</span>
                                <span class="infoStar">
                                    <i class="fa-solid fa-star"></i>
                                    <i class="fa-solid fa-star"></i>
                                    <i class="fa-solid fa-star"></i>
                                    <i class="fa-regular fa-star"></i>
                                    <i class="fa-regular fa-star"></i>
                                </span>
                            </div>
                        </div>
                        <div class="userDetails3">
                            <div class="targetHeading">Target groups</div>
                            <div class="targetgroups d-flex flex-wrap">
                                <div class="targetgroup">Age <br><strong class="ages"></strong></div>
                                <div class="targetgroup">Language<br><strong class="content_language"></strong></div>
                                <div class="targetgroup">Gender<br><strong class="content_attracts"></strong></div>
                            </div>
                            <div class="targetgroup forTag"> 
                                @foreach(Auth::user()->hashtags as $tags)
                                <span >#{{$tags->tags}}</span> 
                                @endforeach
                            </div>
                        </div>
                        <div class="userDetails4">
                            <span class="infoPrice_{{Auth::user()->advertisingMethodPrice->video_media}}">€ {{Auth::user()->advertisingMethodPrice->video_price}}</span>
                        </div>                             
                    </div>
                </div>
                @endif




                @if(isset(Auth::user()->advertisingMethodPrice->livestream) &&  Auth::user()->advertisingMethodPrice->livestream== 1 && Auth::user()->advertisingMethodPrice->livestream_media!= ''  ) 
                @php
 
                if(Auth::user()->advertisingMethodPrice->livestream_media == 'facebook')
                {
                     $media = $social_connect_facebook ; 
                }elseif(Auth::user()->advertisingMethodPrice->livestream_media == 'instagram')
                {
                     $media = $social_connect_instagram ; 
                }elseif(Auth::user()->advertisingMethodPrice->livestream_media == 'twitter')
                {
                     $media = $social_connect_twitter ; 
                }elseif(Auth::user()->advertisingMethodPrice->livestream_media == 'youtube')
                {
                     $media = $social_connect_youtube ; 
                }elseif(Auth::user()->advertisingMethodPrice->livestream_media == 'twitch')
                {
                     $media = $social_connect_twitch ; 
                }elseif(Auth::user()->advertisingMethodPrice->livestream_media  == 'tiktok')
                {
                     $media = $social_connect_tiktok ; 
                }elseif(Auth::user()->advertisingMethodPrice->livestream_media == 'snapchat')
                {
                     $media = $social_connect_snapchat ; 
                }

                @endphp
                <div class="flexUser data_7 {{Auth::user()->advertisingMethodPrice->livestream_media}}" style="display: block;">
                    <div class="oii">Livestream</div>
                    <div class="userDetails newClass{{Auth::user()->id}}" id="{{Auth::user()->id}}">  
                        <div class="userDetails1 d-flex">
                            <a href="{{ isset($media)?$media->url:''}}" target="_blank">
                                <span class="handelname">{{isset($media)?$media->name:''}}  {{Auth::user()->request }}</span> 
                            </a>
                            <span class="handelpletform">
                                <img src="{{ asset('/') }}/assets/front-end/images/icons/campaigns-{{Auth::user()->advertisingMethodPrice->livestream_media}}.png" class="small-icon" alt=""> 
                            </span>
                        </div>                        
                        <div class="userDetails2">
                            <div class="userDetailImage">
                                <a href="{{ isset($media)?$media->url:''}}" target="_blank"> 
                                    <img src="{{asset('storage/'.(isset($media)?$media->picture:''))}}" class="iconColored" alt="">
                                </a>
                            </div>
                            <div class="userDetailInfo">
                                <span class="infoName">{{isset($media)?$media->followers:''}} Follower</span>
                                <span class="infoStar">
                                    <i class="fa-solid fa-star"></i>
                                    <i class="fa-solid fa-star"></i>
                                    <i class="fa-solid fa-star"></i>
                                    <i class="fa-regular fa-star"></i>
                                    <i class="fa-regular fa-star"></i>
                                </span>
                            </div>
                        </div>
                        <div class="userDetails3">
                            <div class="targetHeading">Target groups</div>
                            <div class="targetgroups d-flex flex-wrap">
                                <div class="targetgroup">Age <br><strong class="ages"></strong></div>
                                <div class="targetgroup">Language<br><strong class="content_language"></strong></div>
                                <div class="targetgroup">Gender<br><strong class="content_attracts"></strong></div>
                            </div>
                            <div class="targetgroup forTag"> 
                                @foreach(Auth::user()->hashtags as $tags)
                                <span >#{{$tags->tags}}</span> 
                                @endforeach
                            </div>
                        </div>
                        <div class="userDetails4">
                            <span class="infoPrice_{{Auth::user()->advertisingMethodPrice->livestream_media}}">€ {{Auth::user()->advertisingMethodPrice->livestream_price}}</span>
                        </div>                             
                    </div>
                </div>
                @endif


            </div>
            @endif
        </div>



        <div class="tabsram-in">
            <div class="orcTitle">
                Your competition 
            </div> 

             @if(isset(Auth::user()->advertisingMethodPrice) ) 
                <div class="orcCont">
                    @if(isset(Auth::user()->advertisingMethodPrice->sharecontent) &&  Auth::user()->advertisingMethodPrice->sharecontent== 1 && Auth::user()->advertisingMethodPrice->sharecontent_media!= '' && !empty($sharecontentData) )                   
                    <div class="flexUser data_1 {{$sharecontentData->media}}"  style="display: block;" >
                        <div class="oii">Share Content</div>
                        <div class="userDetails newClass1" id="1">  
                            <div class="userDetails1 d-flex">
                                <a href="{{$sharecontentData->url}}" target="_blank"> 
                                    <span class="handelname">{{isset($sharecontentData)?$sharecontentData->username:''}} </span> 
                                </a>
                                <span class="handelpletform">
                                    <img src="{{ asset('/') }}/assets/front-end/images/icons/campaigns-{{$sharecontentData->media}}.png" class="small-icon" alt=""> 
                                </span>
                            </div>                        
                            <div class="userDetails2">
                                <div class="userDetailImage">
                                    <a href="{{$sharecontentData->url}}" target="_blank"> 
                                        <img src="{{asset('storage/'.$sharecontentData->picture)}}" class="iconColored" alt="">
                                    </a>
                                </div>
                                <div class="userDetailInfo">
                                    <span class="infoName">{{$sharecontentData->distance}} Follower</span>
                                    <span class="infoStar">
                                        <i class="fa-solid fa-star"></i>
                                        <i class="fa-solid fa-star"></i>
                                        <i class="fa-solid fa-star"></i>
                                        <i class="fa-regular fa-star"></i>
                                        <i class="fa-regular fa-star"></i>
                                    </span>
                                </div>
                            </div>
                            <div class="userDetails3">
                                <div class="targetHeading">Target groups</div>
                                <div class="targetgroups d-flex flex-wrap">
                                    <div class="targetgroup">Age <br>
                                        <strong>{{$sharecontentData->ages}}</strong>
                                    </div>
                                    <div class="targetgroup">Language<br>
                                        <strong>{{$sharecontentData->content_language}}</strong>
                                    </div>
                                    <div class="targetgroup">Gender<br>
                                        <strong>{{$sharecontentData->content_attracts}}</strong>
                                    </div>
                                </div>
                                <div class="targetgroup forTag">
                                    @php  $user = App\Models\User::find($sharecontentData->user_id); @endphp
                                    @if(isset($user->hashtags))
                                    @foreach($user->hashtags as $tags)
                                    <span>#{{$tags->tags}}</span>
                                    @endforeach
                                    @endif
                                </div>
                            </div>
                            <div class="userDetails4"> 
                                <?php 
                                    $newPrice = 0;
                                    $addNewPrice = 0;
                                    $newPrice = $sharecontentData->sharecontent_price ; 
                                    $src = URL:: asset('/').'/assets/front-end/images/col_icon_{{$sharecontentData->media}}.png'; 
                                ?> 
                                    € {{$newPrice}}
                            </div>                            
                        </div>
                    </div> 
                    @endif

                    @if(isset(Auth::user()->advertisingMethodPrice->video) &&  Auth::user()->advertisingMethodPrice->video== 1 && Auth::user()->advertisingMethodPrice->video_media!= '' && !empty($videoData) )                   
                    <div class="flexUser data_2 {{$videoData->media}}"  style="display: block;" >
                        <div class="oii">Video on Demand</div>
                        <div class="userDetails newClass2" id="2">  
                            <div class="userDetails1 d-flex">
                                <a href="{{$videoData->url}}" target="_blank"> 
                                    <span class="handelname">{{isset($videoData)?$videoData->username:''}} </span> 
                                </a>
                                <span class="handelpletform">
                                    <img src="{{ asset('/') }}/assets/front-end/images/icons/campaigns-{{$videoData->media}}.png" class="small-icon" alt=""> 
                                </span>
                            </div>                        
                            <div class="userDetails2">
                                <div class="userDetailImage">
                                    <a href="{{$videoData->url}}" target="_blank"> 
                                        <img src="{{asset('storage/' . $videoData->picture}}" class="iconColored" alt="">
                                    </a>
                                </div>
                                <div class="userDetailInfo">
                                    <span class="infoName">{{$videoData->followers}} Follower</span>
                                    <span class="infoStar">
                                        <i class="fa-solid fa-star"></i>
                                        <i class="fa-solid fa-star"></i>
                                        <i class="fa-solid fa-star"></i>
                                        <i class="fa-regular fa-star"></i>
                                        <i class="fa-regular fa-star"></i>
                                    </span>
                                </div>
                            </div>
                            <div class="userDetails3">
                                <div class="targetHeading">Target groups</div>
                                <div class="targetgroups d-flex flex-wrap">
                                    <div class="targetgroup">Age <br>
                                        <strong>{{$videoData->ages}}</strong>
                                    </div>
                                    <div class="targetgroup">Language<br>
                                        <strong>{{$videoData->content_language}}</strong>
                                    </div>
                                    <div class="targetgroup">Gender<br>
                                        <strong>{{$videoData->content_attracts}}</strong>
                                    </div>
                                </div>
                                <div class="targetgroup forTag">  
                                    @php  $user = App\Models\User::find($videoData->user_id); @endphp
                                    @if(isset($user->hashtags))
                                    @foreach($user->hashtags as $tags)
                                    <span>#{{$tags->tags}}</span>
                                    @endforeach
                                    @endif
                                </div>
                            </div>
                            <div class="userDetails4"> 
                                <?php 
                                    $newPrice = 0;
                                    $addNewPrice = 0;
                                    $newPrice = $videoData->video_price ; 
                                    $src = URL:: asset('/').'/assets/front-end/images/col_icon_{{$videoData->media}}.png'; 
                                ?> 
                                    € {{$newPrice}}
                            </div>                            
                        </div>
                    </div> 
                    @endif 

                    @if(isset(Auth::user()->advertisingMethodPrice->livestream) &&  Auth::user()->advertisingMethodPrice->livestream== 1 && Auth::user()->advertisingMethodPrice->livestream_media!= '' && !empty($livestreamData) )                   
                    <div class="flexUser data_3 {{$livestreamData->media}}"  style="display: block;" >
                        <div class="oii">Livestream</div>
                        <div class="userDetails newClass3" id="3">  
                            <div class="userDetails1 d-flex">
                                <a href="{{$livestreamData->url}}" target="_blank"> 
                                    <span class="handelname">{{isset($livestreamData)?$livestreamData->username:''}}  </span> 
                                </a>
                                <span class="handelpletform">
                                    <img src="{{ asset('/') }}/assets/front-end/images/icons/campaigns-{{$livestreamData->media}}.png"  class="small-icon"alt=""> 
                                </span>
                            </div>                        
                            <div class="userDetails2">
                                <div class="userDetailImage">
                                    <a href="{{$livestreamData->url}}" target="_blank"> 
                                        <img src="{{asset('storage/' . $livestreamData->picture}}" class="iconColored" alt="">
                                    </a>
                                </div>
                                <div class="userDetailInfo">
                                    <span class="infoName">{{$livestreamData->followers}} Follower</span>
                                    <span class="infoStar">
                                        <i class="fa-solid fa-star"></i>
                                        <i class="fa-solid fa-star"></i>
                                        <i class="fa-solid fa-star"></i>
                                        <i class="fa-regular fa-star"></i>
                                        <i class="fa-regular fa-star"></i>
                                    </span>
                                </div>
                            </div>
                            <div class="userDetails3">
                                <div class="targetHeading">Target groups</div>
                                <div class="targetgroups d-flex flex-wrap">
                                    <div class="targetgroup">Age <br>
                                        <strong>{{$livestreamData->ages}}</strong>
                                    </div>
                                    <div class="targetgroup">Language<br>
                                        <strong>{{$livestreamData->content_language}}</strong>
                                    </div>
                                    <div class="targetgroup">Gender<br>
                                        <strong>{{$livestreamData->content_attracts}}</strong>
                                    </div>
                                </div>
                                <div class="targetgroup forTag">
                                    @php  $user = App\Models\User::find($livestreamData->user_id); @endphp
                                    @if(isset($user->hashtags))
                                    @foreach($user->hashtags as $tags)
                                    <span>#{{$tags->tags}}</span>
                                    @endforeach
                                    @endif
                                </div>
                            </div>
                            <div class="userDetails4"> 
                                <?php 
                                    $newPrice = 0;
                                    $addNewPrice = 0;
                                    $newPrice = $livestreamData->livestream_price ; 
                                    $src = URL:: asset('/').'/assets/front-end/images/col_icon_{{$livestreamData->media}}.png'; 
                                ?> 
                                    € {{$newPrice}}
                            </div>                            
                        </div>
                    </div> 
                    @endif

                </div>
            @endif
              
        </div>
    </div>  --}}
</div>
  
</div>

<script type="text/javascript">
    
            $( ".content_language" ).each( function () {
                var star1 = $(this);
                star1.html($('select[name="content_language"]').val());
            });
    
            $( ".ages" ).each( function () {
                var star2 = $(this);
                star2.html($('select[name="ages"]').val());
            });
            $( ".content_attracts" ).each( function () {
                var star3 = $(this);
                star3.text($('input[name="content_attracts"]:checked').val());
            });
            $( ".hashtags1" ).each( function () {
                var star4 = $(this);
                star4.text('#'+$('.hashtags_one').val());
            });
            $( ".hashtags2" ).each( function () {
                var star5 = $(this);
                star5.text('#'+$('.hashtags_two').val());
            });
            $( ".hashtags3" ).each( function () {
                var star6 = $(this);
                star6.text('#'+$('.hashtags_three').val());
            });

            $('.infoPrice_youtube').html($('input[name="video_price_youtube"]').val());
            $('.infoPrice_twitter').html($('input[name="video_price_twitter"]').val());
            $('.infoPrice_twitch').html($('input[name="video_price_twitch"]').val());
            $('.infoPrice_facebook').html($('input[name="video_price_facebook"]').val());
            $('.infoPrice_instagram').html($('input[name="video_price_instagram"]').val());

            $('.liveStream_youtube').html($('input[name="livestream_price_youtube"]').val());
            $('.liveStream_twitter').html($('input[name="livestream_price_twitter"]').val());
            $('.liveStream_twitch').html($('input[name="livestream_price_twitch"]').val());
            $('.liveStream_facebook').html($('input[name="livestream_price_facebook"]').val());
            $('.liveStream_instagram').html($('input[name="livestream_price_instagram"]').val());

            $('.share_youtube').html($('input[name="share_price_youtube"]').val());
            $('.share_twitter').html($('input[name="share_price_twitter"]').val());
            $('.share_twitch').html($('input[name="share_price_twitch"]').val());
            $('.share_facebook').html($('input[name="share_price_facebook"]').val());
            $('.share_instagram').html($('input[name="share_price_instagram"]').val());

</script>