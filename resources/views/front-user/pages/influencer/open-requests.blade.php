@extends('front-user.layouts.master_user_dashboard')

@section('content')
    <style>
        span.smalltext {
            position: relative !important;
            text-align: left;
            left: 11px;
            bottom: 0px;
        }
    </style>
    <section id="campaignForm">
        <h1 class="section-heading"><span>Open Requests</span></h1>

        <div class="row">
            <div class="offset-md-8 col-md-4 col-sm-6 section-button" style="padding-right: 20px;">
                <a href="{{ url('request-history') }}" class="btn btn-outline-secondary top-right-button btn-history"
                    style="float: right;"><img src="{{ asset('/assets/front-end/images/new/history.svg') }}"> Request History
                </a>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12 new_card_row">
                <table class="connectPrising requestCamp w-100">
                    @php  $count=0; @endphp
                    @if (isset($influencerCampaignDetails))
                        @foreach ($influencerCampaignDetails as $influencerCampaignDetail)
                            @php
                                $influencerCampaignDetail = $influencerCampaignDetail;
                                $created_date = date('Y-m-d', strtotime($influencerCampaignDetail->created_at));
                                $campaignDate = date('Y-m-d', strtotime($created_date . '+3 days'));
                                $date = date('Y-m-d');
                            @endphp
                            @if (
                                $influencerCampaignDetail->invoice_id == '' &&
                                $influencerCampaignDetail->review != '2' &&
                                $influencerCampaignDetail->refund_reason == '' &&
                                $influencerCampaignDetail->status != 'Cancelled'
                            )
                                @if ($date <= $campaignDate)
                                    @php $count++; @endphp

                                    {{-- Desktop View --}}
                                    <div class="campaign-card desktop-view">
                                        @include('front-user.pages.desktop.influencer.open-requests', ['influencerCampaignDetail' => $influencerCampaignDetail])
                                    </div>

                                    {{-- Mobile view --}}
                                    <div class="campaign-card mobile-view" style="display: none;">
                                        @include('front-user.pages.mobile.influencer.open-requests', ['influencerCampaignDetail' => $influencerCampaignDetail])
                                    </div>

                                    {{-- Modals --}}
                                    @include('front-user.modals.influencer.open-requests', ['influencerCampaignDetail' => $influencerCampaignDetail])

                                    <x-modals.influencer.check-request :influencer-campaign-data="$influencerCampaignDetail" :campaign-request-time="$campaignRequestTime" />
                                @endif
                            @endif
                        @endforeach
                    @endif
                    @if ($count == 0)
                        <div class="no-data-div">
                            <img src="{{ asset('/assets/front-end/images/icons/icon-no-data-image.png') }}"
                                alt="">
                            <div class="no-data-contant">
                                Sorry, you have no requests at the moment. <br />
                                You will get notified by e-mail when you get a request, stay tuned!
                            </div>
                        </div>
                    @endif
                </table>
            </div>
        </div>
    </section>
    <div class="loaderss" id="pageLoader">
        <img src="{{ asset('/assets/front-end/images/loading-loading-forever.gif') }}" alt="">
    </div>
@endsection

@section('script_links')
@endsection

@section('script_codes')
    <script type="text/javascript">
        $(window).on('load', function() {
            console.log($('#req_count').val());
            if ($('#req_count').val() >= 5 && getCookie("show_popup") != "no") {
                $('#manyrequest').modal('show');
                var timeToAdd = 24 * 60 * 60 * 1000;
                var date = new Date();
                var expiryTime = parseInt(date.getTime()) + timeToAdd;
                date.setTime(expiryTime);
                var utcTime = date.toUTCString();
                document.cookie = "show_popup=no; expires=" + utcTime + ";";
            }
        });

        function getCookie(cname) {
            let name = cname + "=";
            let decodedCookie = decodeURIComponent(document.cookie);
            let ca = decodedCookie.split(';');
            for (let i = 0; i < ca.length; i++) {
                let c = ca[i];
                while (c.charAt(0) == ' ') {
                    c = c.substring(1);
                }
                if (c.indexOf(name) == 0) {
                    return c.substring(name.length, c.length);
                }
            }
            return "";
        }
    </script>
    <script type="text/javascript">
        influencerCampaignDetails = @json($influencerCampaignDetails);
        if (influencerCampaignDetails.length > 0) {
            influencerCampaignDetails.forEach(timer);

            function timer(itemD, index) {
                // Set the date we're counting down to
                if (itemD['request'] == 1 && itemD['invoice_id'] != '') {
                    console.log('a', itemD['id'])
                    var campaignDate = new Date(itemD['created_at']);

                    campaignDate.setDate(campaignDate.getDate() + parseInt(3) + parseInt(3));

                } else {
                    console.log('b', itemD['id'])
                    var campaignDate = new Date(itemD['created_at']);
                    campaignDate.setDate(campaignDate.getDate() + parseInt(@json($campaignRequestTime->request_time)));
                    console.log(campaignDate)
                }
                var now = new Date();

                // Update the count down every 1 second
                let timerObjs = {};
                timerObjs[itemD['campaign_id']] = setInterval(function() {

                    // Get today's date and time
                    var now = new Date().getTime();
                    //    var now = new Date(new Date().setHours(0,0,0,0)).getTime();

                    // Find the distance between now and the count down date
                    var distance = campaignDate - now;
                    // Time calculations for days, hours, minutes and seconds
                    var days = Math.floor(distance / (1000 * 60 * 60 * 24));
                    var hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                    var minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                    var seconds = Math.floor((distance % (1000 * 60)) / 1000);

                    if (days < 10 && days != 0) {
                        days = '0' + days;
                    }

                    if (hours < 10) {
                        hours = '0' + hours;
                    }

                    if (minutes < 10) {
                        minutes = '0' + minutes;
                    }

                    if (seconds < 10) {
                        seconds = '0' + seconds;
                    }

                    $("#timer" + itemD['campaign_id']).text(days + " d " + hours + " h " + minutes + " min");
                    $("#timer1" + itemD['campaign_id']).text(days + " d " + hours + " h " + minutes + " min");
                    $("#timerMobile" + itemD['campaign_id']).text(days + " d " + hours + " h " + minutes + " min");

                    // If the count down is over, write some text
                    if (distance < 0) {
                        clearInterval(timerObjs[itemD['campaign_id']]);
                        $("#timer" + itemD['campaign_id']).text("EXPIRED");
                        $("#timer1" + itemD['campaign_id']).text("EXPIRED");
                        $("#timerMobile" + itemD['campaign_id']).text("EXPIRED");
                    }
                }, 1000);
            }
        }


        $(document).ready(function() {
            var hash = window.location.hash;
            if (hash != '') {
                console.log('#heading' + hash.substring(1));
                console.log('#collapse' + hash.substring(1));
                $('#myTab button[data-bs-target="#active-campaigns"]').tab('show');
                // $('#heading'+hash.substring(1)+' button[data-bs-target="#collapse'+hash.substring(1)+'"]').tab('show');
            }

        });

        function requestTimePopup(influencer_request_id, text) {
            console.log(influencer_request_id);
            $('#request_influencer_request_id').val(influencer_request_id);
            $('#request_text').html(text);
        }


        function confirmSocial(influencer_request_id, post_id, advertising, media, text, media_url, type, published_at,
            post_type, thumbnail, like, comment, view, share, friend, favorite) {

            var url = '{{ asset('storage/') }}' + '/' + media_url;
            console.log(post_type);
            $('#content_text').html(text);
            $('#content_influencer_request_id').val(influencer_request_id);
            $('#content_post_id').val(post_id);
            $('#content_advertising').val(advertising);
            $('#content_media').val(media);
            $('#content_media_url').val(media_url);
            $('#content_url').attr('href', media_url);
            $('#content_url_img').hide();
            $('#content_url_video_tag').hide();
            $('#viewLink').hide();

            if (like != '') {
                $('#like').html(like);
            } else {
                $('#likediv').hide();
            }

            if (comment != '') {
                $('#comment').html(comment);
            } else {
                $('#commentdiv').hide();
            }
            if (view != '') {
                $('#view').html(view);
            } else {
                $('#viewdiv').hide();
            }
            if (share != '') {
                $('#share').html(share);
            } else {
                $('#sharediv').hide();
            }
            if (friend != '') {
                $('#friend').html(friend);
            } else {
                $('#frienddiv').hide();
            }
            if (favorite != '') {
                $('#favorite').html(favorite);
            } else {
                $('#favoritediv').hide();
            }
            console.log(media_url.includes('http'));

            if (post_type == 'Polls') {
                $('#content_display').html('Survey title');
            }
            if (media_url != '' && !media_url.includes('http')) {
                if (type == 'video') {
                    $('#content_url_video_tag').show();
                    $('#content_url_video').attr('src', url);
                    $("#content_url_video_tag")[0].load();
                    $('#content_url_img').hide();
                    // $('#viewLink').hide();
                }
                if (type == 'photo') {
                    $('#content_url_img').show();
                    $('#content_url_img').attr('src', url);
                    $('#content_url_video_tag').hide();
                    // $('#viewLink').hide();
                }
                $('#viewLink').show();
                $('#viewLink').attr('href', thumbnail);
            }
            if (media_url != '' && media_url.includes('http')) {
                $('#viewLink').show();
                $('#viewLink').attr('href', media_url);
                $('#content_url_video_tag').hide();
                $('#content_url_img').hide();
            }
            $('#content_published_at').val(published_at);
        }

        function acceptRequest(id) {

            $.ajax({
                url: "{{ URL::to('/accept-request') }}/" + id,
                method: 'GET',
            }).done(function(data) {
                $(document).ready(function() {
                    $('button[data-bs-toggle="tab"]').on('show.bs.tab', function(e) {
                        localStorage.setItem('activeTab', $(e.target).attr('data-bs-target'));
                    });
                    var activeTab = localStorage.getItem('activeTab');
                    if (activeTab) {
                        $('#myTab button[data-bs-target="' + activeTab + '"]').tab('show');
                    }
                });
                console.log('accept-request');
                window.location.reload();
            }).fail(function() {

            });
        }

        function requestReject(id) {
            console.log('REJECT');
            $("#pageLoader").show()
            $('#requestFormSubmit' + id).submit();
        }

        function requestSubmitbutton(id) {
            $("#pageLoader").show()
            $.ajax({
                url: "{{ URL::to('/initiate-influencer-campaign-submission') }}/" + id,
                method: 'GET',
            }).done(function(data) {
                $('.campHistory').html(data);
                $('#requestSubmit' + id).modal('show');
                $("#pageLoader").hide()
            }).fail(function() {
                console.log('fail');
                $("#pageLoader").hide()
            });
        }

        $(document).on("click", ".continue-link.continue-open", function() {
            var get_id = $(this).closest(".tab-pane").attr("id").replace(/general-information/, '')
            $("#order-detail-tab" + get_id).click();
        })

        document.addEventListener('DOMContentLoaded', (event) => {
            const btn_yes = document.querySelector('.loaderShow.table-btn.green-btn.ds.accept');
            const btn_no = document.querySelector('.table-btn.red-btn.ds.reject');
            const img_yes = btn_yes.querySelector('.icon-image_yes');
            const img_no = btn_no.querySelector('.icon-image_no');
            const originalSrc_yes = img_yes.src;
            const originalSrc_no = img_no.src;
            const hoverSrc_yes =
                "{{ asset('/assets/front-end/images/icons/icon-green-check-new_hover.png') }}"; // Replace with the hover image source
            const hoverSrc_no =
                "{{ asset('/assets/front-end/images/icons/close-one-new_hover.png') }}"; // Replace with the hover image source

            btn_yes.addEventListener('mouseover', () => {
                img_yes.src = hoverSrc_yes;
                img_yes.width = 38;
            });

            btn_yes.addEventListener('mouseout', () => {
                img_yes.src = originalSrc_yes;
                // img_yes.removeAttr('width');
            });

            btn_no.addEventListener('mouseover', () => {
                img_no.src = hoverSrc_no;
                img_no.width = 38;
            });

            btn_no.addEventListener('mouseout', () => {
                img_no.src = originalSrc_no;
                // img_no.removeAttr('width');
            });
        });
    </script>
@endsection
<x-modals.influencer.campaign-phases />
