@extends('front-user.layouts.master_user_dashboard')

@section('content')
    <style>
        span.smalltext {
            position: relative !important;
            text-align: left;
            left: 11px;
            bottom: 0px;
        }

        @media screen and (max-width: 768px) {
            .btn-cancel-new {
                width: 124% !important;
            }

            .btn-show-details {
                width: 124% !important;
            }
        }

        .modal .tasks,
        .modal .upload,
        .modal .confirm {
            margin-top: 20px;
            text-align: left;
            border: 1px solid #AD80FF;
            padding: 10px;
            border-radius: 10px;
        }

        .modal .tasks label,
        .modal .upload label,
        .modal .confirm label {
            display: block;
            font-size: 16px;
            margin-bottom: 10px;
            color: #333;
        }

        .modal .tasks div,
        .modal .confirm div {
            display: flex;
            align-items: center;
            justify-content: center;
            /* Center-align the content */
        }

        .modal .tasks input[type="checkbox"],
        .modal .confirm input[type="checkbox"] {
            margin-right: 10px;
        }

        .modal .upload input[type="file"] {
            display: none;
        }

        .modal .upload .upload-label {
            border: 2px dashed #b388ff;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            color: #b388ff;
            cursor: pointer;
        }

        img[name="pdf-icon-btn"] {
            filter: brightness(0) invert(1);
        }
    </style>
    <section id="campaignForm">
        <div>
            <h1 class="section-heading"><span>Active Campaigns</span></h1>

            <div class="row">
                <div class="offset-md-8 col-md-4 col-sm-6 section-button">
                    <a href="{{ url('campaign-history') }}" class="btn btn-outline-secondary top-right-button btn-history" style="float: right;">
                        <img src="{{ asset('/assets/front-end/images/new/history.svg') }}"> Campaign History
                    </a>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12 new_card_row">
                    @php $count = 0; @endphp
                    @if (isset($influencerData))
                        @foreach ($influencerData as $influencerDataItem)
                            @php
                                $influencerRequestDetails = App\Models\InfluencerRequestDetail
                                    ::where('compaign_id', $influencerDataItem->campaign_id)
                                    ->where('influencer_detail_id', $influencerDataItem->influencer_detail_id)
                                    ->get();
                            @endphp
                            @if (
                                $influencerDataItem->request == 1 &&
                                $influencerDataItem->invoice_id != '' &&
                                $influencerDataItem->review != '1' &&
                                $influencerDataItem->refund_reason == ''
                            )
                                @if ($influencerDataItem->finish != '1')
                                    @php $count++; @endphp

                                    {{-- Desktop View --}}
                                    <div class="campaign-card desktop-view">
                                        @include('front-user.pages.desktop.influencer.active-campaigns', [
                                            'influencerDataItem' => $influencerDataItem,
                                            'influencerRequestDetails' => $influencerRequestDetails
                                        ])
                                    </div>
                                    
                                    {{-- Mobile View --}}
                                    <div class="campaign-card mobile-view" style="display:none;">
                                        @include('front-user.pages.mobile.influencer.active-campaigns', [
                                            'influencerDataItem' => $influencerDataItem,
                                            'influencerRequestDetails' => $influencerRequestDetails
                                        ])
                                    </div>
                                    
                                    {{-- Modals --}}
                                    @include('front-user.modals.influencer.active-campaigns', ['influencerDataItem' => $influencerDataItem])
                                    @include('front-user.modals.influencer.initiate-campaign-submission', ['influencerDataItem' => $influencerDataItem])
                                    @include('front-user.modals.influencer.confirm-campaign-submission', ['influencerDataItem' => $influencerDataItem])
                                @endif
                            @endif
                        @endforeach
                    @endif
                    @if ($count == 0)
                        <div class="no-data-div">
                            <img src="{{ asset('/assets/front-end/images/icons/icon-no-data-image.png') }}" alt="">
                            <div class="no-data-contant">You currently have no running campaigns</div>
                        </div>
                    @endif
                </div>
            </div>
    </section>
    <div class="loaderss" id="pageLoader">
        <img src="{{ asset('/assets/front-end/images/loading-loading-forever.gif') }}" alt="">
    </div>
@endsection

@section('script_links')
@endsection

@section('script_codes')
    <script type="text/javascript">
        $(document).ready(function() {
            var review = @json($review);
            if (review == 1) {
                $('#thankYouContact').modal('show');
            }
        });

        let isComplained = {{ isset($influencerDataItem->influencer_request_accepts->complaints) && $influencerDataItem->influencer_request_accepts->complaints->status ? '1' : '0' }}
        dates = @json($influencerData);
        if (dates.length > 0) {
            dates.forEach(timer);

            function timer(itemD, index) {
                // Set the date we're counting down to
                if (itemD['is_paused'] == 1 || itemD['is_complained'] == 1) {
                    $("#timer" + itemD['compaign_id']).text("Paused");
                    $("#timer" + itemD['compaign_id']).next('span').remove();
                } else if (
                    itemD['request'] == 1 &&
                    itemD['invoice_id'] != '' &&
                    itemD['review'] != '1' &&
                    itemD['refund_reason'] != '' &&
                    itemD['finish'] != '1'
                ) {
                    var addtime = ((itemD['request_time_accept']) && itemD['request_time_accept'] == '1') ? parseInt(itemD['request_time']) + parseInt(itemD['time']) : itemD['time'];
                    // Convert "YYYY-MM-DD HH:mm:ss" to UTC Date object
                    var acceptTimeParts = itemD['accept_time'].split(' ');
                    var dateParts = acceptTimeParts[0].split('-');
                    var timeParts = acceptTimeParts[1].split(':');
                    var campaignDate = new Date(Date.UTC(
                        parseInt(dateParts[0]),           // year
                        parseInt(dateParts[1]) - 1,       // month (0-based)
                        parseInt(dateParts[2]),           // day
                        parseInt(timeParts[0]),           // hour
                        parseInt(timeParts[1]),           // minute
                        parseInt(timeParts[2])            // second
                    ));

                    if (itemD['social_post_id'] == null || itemD['social_post_id'] == '') {
                        campaignDate.setDate(campaignDate.getDate() + parseInt(addtime));
                    } else {
                        campaignDate.setDate(campaignDate.getDate() + parseInt(addtime) + parseInt(7));
                    }

                    campaignDate = campaignDate.getTime();
                    var now = new Date();

                    // Update the count down every 1 second
                    var x = setInterval(function() {
                        // Get today's date and time
                        var now = new Date().getTime();
                        // Find the distance between now and the count down date
                        var distance = campaignDate - now;
                        // Time calculations for days, hours, minutes and seconds
                        var days = Math.floor(distance / (1000 * 60 * 60 * 24));
                        var hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                        var minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                        var seconds = Math.floor((distance % (1000 * 60)) / 1000);

                        if (days < 10 && days != 0) {
                            days = '0' + days;
                        }
                        if (hours < 10) {
                            hours = '0' + hours;
                        }
                        if (minutes < 10) {
                            minutes = '0' + minutes;
                        }
                        if (seconds < 10) {
                            seconds = '0' + seconds;
                        }
                        // Output the result in an element with id="demo"
                        $("#timer" + itemD['compaign_id']).text(days + " d " + hours + " h " +
                            minutes + " min");
                        $("#timer1" + itemD['compaign_id']).text(days + " d " + hours + " h " +
                            minutes + " min");
                        $("#mobiletimer" + itemD['compaign_id']).text(days + " d " + hours + " h " +
                            minutes + " min");

                        // If the count down is over, write some text
                        if (distance < 0) {
                            // console.log('EXPIRED');
                            clearInterval(x);
                            $("#timer" + itemD['compaign_id']).text("EXPIRED");
                            $("#timer1" + itemD['compaign_id']).text("EXPIRED");
                            $("#mobiletimer" + itemD['compaign_id']).text("EXPIRED");
                            $(".startTImer" + itemD['compaign_id']).attr('disabled', 'disabled');
                        }
                    }, 1000);
                }
            }
        }


        $(document).ready(function() {
            var hash = window.location.hash;
            if (hash != '') {
                $('#myTab button[data-bs-target="#active-campaigns"]').tab('show');
            }

            // When modal is hidden, focus to body to avoid js error
            $(document).on('hidden.bs.modal', '.modal', function () {
                // Move focus to a safe element
                $('body').focus();
            });
        });

        function requestTimePopup(influencer_request_id, text) {
            $('#request_influencer_request_id').val(influencer_request_id);
            $('#request_text').html(text);
        }

        function acceptRequest(id) {
            $.ajax({
                url: "{{ URL::to('/accept-request') }}/" + id,
                method: 'GET',
            }).done(function(data) {
                $(document).ready(function() {
                    $('button[data-bs-toggle="tab"]').on('show.bs.tab', function(e) {
                        localStorage.setItem('activeTab', $(e.target).attr('data-bs-target'));
                    });
                    var activeTab = localStorage.getItem('activeTab');
                    if (activeTab) {
                        $('#myTab button[data-bs-target="' + activeTab + '"]').tab('show');
                    }
                });
                window.location.reload();
            }).fail(function() {});
        }

        function requestReject(id) {
            $("#pageLoader").show()
            $('#requestFormSubmit' + id).submit();
        }

        function influencerInitiateCampaignSubmissionSubmitButton(id) {
            $("#pageLoader").show()
            $.ajax({
                url: "{{ URL::to('/initiate-influencer-campaign-submission') }}/" + id,
                method: 'GET',
            }).done(function(data) {
                $('#influencerInitiatedCampaignSubmission--socialPosts-' + id).html(data);
                // The modal is in resources/views/front-user/modals/influencer/initiate-campaign-submission.blade.php
                $('#influencerInitiatedCampaignSubmission' + id).modal('show');
                $("#pageLoader").hide();
            }).fail(function() {
                $("#pageLoader").hide();
            });
        }

        function requestCancelbutton(id) {
            $("#pageLoader").show()
            $.ajax({
                url: "{{ URL::to('/request-cancel') }}/" + id,
                method: 'GET',
            }).done(function(data) {
                $("#pageLoader").hide();
                setTimeout(function() {
                    toastr.success('Request cancelled successfully');
                }, 1000)
                window.location.reload();
            }).fail(function() {
                $("#pageLoader").hide();
            });
        }

        $(document).on("click", ".et-submit.accept", function() {
            function isEmpty(el) {
                return !$.trim(el.html())
            }
        });

        $("[value='Yes, finish the request']").click(function() {
            $(this).closest("form").submit(function(e) {
                $("#pageLoader").show();
            });
        });

        let rowId = '';

        $(document).on('click', 'a[data-toggle=pause_campaign]', function() {
            event.preventDefault();
            rowId = $(this).data('id');
            $('#influencerInitiatedCampaignSubmission' + rowId).modal('hide');
            $('#pause_campaign_modal').modal('toggle');
        })

        $(document).on('click', 'a[data-toggle=pause_campaign_next_btn]', function() {
            event.preventDefault();
            if ($('#pause_check_btn').prop('checked')) {
                $('#pause_campaign_error').hide();
                $('#pause_campaign_modal').modal('hide');
                $('#camp_rowId').val(rowId);
                $('#pause_campaign_submit_modal').modal('toggle');
            } else {
                $('#pause_campaign_error').show();
            }
        });

        $('#file-upload').on('change', function() {
            var previewContainer = $('#survey_image_preview');
            previewContainer.empty();

            var files = this.files;

            $.each(files, function(index, file) {
                if (file.type.startsWith('image/')) {
                    var reader = new FileReader();
                    reader.onload = function(e) {
                        var img = $('<img>').attr('src', e.target.result).css({
                            'max-width': '150px',
                            'margin': '10px'
                        });
                        previewContainer.append(img);
                    };
                    reader.readAsDataURL(file);
                }
            });
        });
    </script>
@endsection
<x-modals.influencer.campaign-phases />
