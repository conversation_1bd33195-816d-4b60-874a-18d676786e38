@extends('front-user.layouts.master_user_dashboard')

@section('content')
    <style>
        @media screen and (max-width: 768px) {
            .campaign-table img {
                width: 19px;
                height: 19px;
            }

            .campaign-table {
                margin-left: 15px;
            }

            .new-tabs-ul {
                padding-left: 15% !important;
            }
        }

        img[name="pdf-icon-btn"] {
            filter: brightness(0) invert(1);
        }
    </style>
    <section id="campaignForm">
        <h1 class="section-heading"><span>Campaign History</span></h1>
        <div class="row">
            <div class="col-md-12 new_card_row">
                <div class="accordion" id="accordionHistory">
                    @if (count($history) > 0)
                        @php $i = 1; @endphp
                        @foreach ($history as $row)
                            @php $platform_invoices = App\Models\Invoice::where('campaign_id', $row->compaign_id)->where('payment_type', 'Platform_Payment')->get(); @endphp
                            @if (
                                $row->review == '1' ||
                                    $row->review == '0' ||
                                    $row->status == '0' ||
                                    $row->finish == '1' ||
                                    $row->status == 'Cancelled' ||
                                    $row->refund_reason == 'Cancelled' ||
                                    $row->refund_reason == 'Cancelled By Customer')
                                <div class="campaign-card desktop-view">
                                    <div class="row campaign-status-badge-container">
                                        <span class="btn-finished-phase campaign-status-badge">Completed</span>
                                    </div>
                                    <div class="campaign-header">
                                        <div class="header-section">
                                            <div class="campagin_info" style="width: 60%">
                                                <span style="color: #AD80FF; padding: 0 10px;">ID # {{ $row->compaign_id }}</span>
                                                <h4 style="font-weight: 700;  padding: 0 10px;">{{ $row->compaign_title }}</h4>
                                                <div>
                                                    <span class="badge">
                                                        <img src="{{ asset('/assets/front-end/images/new/three_users.svg') }}">
                                                        {{ $row->total_influencer_count }} Influencers
                                                    </span>
                                                    <span class="badge">
                                                        <img src="{{ asset('/assets/front-end/images/new/survey_camp.svg') }}">
                                                        {{ $row->post_type }}
                                                    </span>
                                                    <span class="badge">
                                                        <img style="height: 20px;width:20px"
                                                             src="{{ asset('assets/front-end/images/icons/campaigns-' . $row->media . '.svg') }}"
                                                             alt="{{ $row->media }} icon">
                                                        {{ $row->advertising }}
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="vertical-line"></div>
                                            <div class="details" style="width: 25%; display:flex; flex-direction:column !important;">
                                                <span class="text-success" style="font-size: 30px; font-weight: 700;  padding: 10px;">
                                                    € {{ number_format($row->current_price, 2) }}
                                                </span>
                                                {{-- TODO Show the cumulative insights here for all influencers in this campaign --}}
                                            </div>
                                            <div class="vertical-line"></div>
                                            <div class="details" style="width: 15%; display:flex; flex-direction:column !important;">
                                                <button class="btn btn-show-details" style="width: 80%; margin: 0;"
                                                    target="popup" data-bs-toggle="modal"
                                                    data-bs-target="#requestForm{{ $row->id }}">Show Details
                                                </button>
                                                @foreach ($platform_invoices as $platform_invoice)
                                                    @if (empty($platform_invoice->receipt))
                                                    @php continue; @endphp
                                                    @endif
                                                    <a href="{{ $platform_invoice->receipt }}" target="_blank" class="btn fee-invoice-btn" type="button"
                                                        style="text-align: center; border: solid 1px #AD80FF; color: white; padding: 1px; width: 80%; background-color: #AD80FF; margin-left: 0px; margin-top: 10px;">
                                                        <img name="pdf-icon-btn" src="{{ asset('/assets/front-end/images/icons/pdf-icon.svg') }}" />
                                                        Fee Invoice
                                                    </a>
                                                    @php break; @endphp
                                                @endforeach
                                            </div>
                                            <div class="vertical-line"></div>
                                            <div class="details"
                                                style="width: 5%; display:flex; flex-direction:column !important;">
                                                <span class="circle camp-button dropdown-button collapsed"
                                                    data-bs-toggle="collapse"
                                                    data-bs-target="#desktop-collapse{{ $row->id }}"
                                                    aria-expanded="true"
                                                    aria-controls="desktop-collapse{{ $row->id }}">
                                                    <i class="collapse-icon fas fa-chevron-up" data-toggle="collapse"
                                                        data-target="#campaignDetails"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="desktop-collapse{{ $row->id }}" class="accordion-collapse collapse"
                                        aria-labelledby="heading{{ $row->id }}" data-bs-parent="#accordionHistory"
                                        style="padding: 0px 10px;">
                                        <ul class="nav nav-tabs tabs campaign_statuses_tab row" role="tablist" style="padding-left: 0;">
                                            <li class="nav-item col-2 offset-md-2">
                                                <a style=" border-radius: 5px; text-align: center; border: solid 1px #AD80FF; color:#AD80FF"
                                                    class="nav-link active" data-bs-toggle="tab"
                                                    data-bs-target="#desktop-all{{ $row->id }}" type="button"
                                                    role="tab" aria-controls="desktop-all{{ $row->id }}"
                                                    aria-selected="true">All</a>
                                            </li>
                                            <li class="nav-item col-2">
                                                <a style=" border-radius: 5px; text-align: center; border: solid 1px #AD80FF; color:#AD80FF"
                                                    class="nav-link" data-bs-toggle="tab"
                                                    data-bs-target="#desktop-completed{{ $row->id }}" type="button"
                                                    role="tab" aria-controls="desktop-completed{{ $row->id }}"
                                                    aria-selected="true">Completed</a>
                                            </li>
                                            <li class="nav-item col-2">
                                                <a style=" border-radius: 5px; text-align: center; border: solid 1px #AD80FF; color:#AD80FF"
                                                    class="nav-link" id="desktop-cancelled-tab{{ $row->id }}"
                                                    data-bs-toggle="tab"
                                                    data-bs-target="#desktop-cancelled{{ $row->id }}" type="button"
                                                    role="tab" aria-controls="desktop-cancelled{{ $row->id }}"
                                                    aria-selected="false">Cancelled</a>
                                            </li>
                                            <li class="nav-item col-4">&nbsp;</li>
                                        </ul>
                                        <div class="tab-content">
                                            <div id="desktop-all{{ $row->id }}" role="tabpanel"
                                                aria-labelledby="desktop-all-tab{{ $row->id }}"
                                                style="margin: 0; max-width: 100% !important; font-size:12px !important;"
                                                class="container tab-pane active">
                                                <table class="campaign-table">
                                                    @php $lists = App\Models\InfluencerRequestDetail::where('compaign_id',$row->compaign_id)->get(); @endphp
                                                    @foreach ($lists as $list)
                                                        {{-- TODO A temporary fix for badly/incomplete deletion of influencer data from admin panel  --}}
                                                        @if (empty($list->influencerdetails))
                                                            @php continue; @endphp
                                                        @endif
                                                        @if ($list->finish == 1 && $list->refund_reason == '')
                                                            <tr class="completed">
                                                            @else
                                                            <tr class="cancelled">
                                                        @endif

                                                        @if ($list->finish == 1 && $list->refund_reason == '')
                                                            <td style="width: 10%; text-align:center;">
                                                                <img src="{{ asset('/assets/front-end/images/new/ph_complete.svg') }}"
                                                                    alt="">
                                                            </td>
                                                        @else
                                                            <td style="width: 10%; text-align:center;">
                                                                <img src="{{ asset('/assets/front-end/images/new/ph_cancel.svg') }}"
                                                                    alt="">
                                                            </td>
                                                        @endif

                                                        @php
                                                            $socialLink = App\Models\SocialConnect::where(
                                                                'user_id',
                                                                $list->influencerdetails->user->id,
                                                            )
                                                                ->where('media', $row->media)
                                                                ->first();
                                                        @endphp
                                                        @if ($socialLink != '')
                                                            <td style="width: 25%;">
                                                                <img src="{{ url('/storage/app/' . $socialLink->picture) }}"
                                                                    alt="User">
                                                                <a target="popup" data-bs-toggle="modal"
                                                                    data-bs-target="#influncerdetailpopup{{ $socialLink->id }}">@
                                                                    {{ isset($socialLink->name) ? $socialLink->name : $list->influencerdetails->user->first_name }}</a>
                                                            </td>
                                                            <td style="width: 15%;">
                                                                {{ @$socialLink->followers }}
                                                                {{ $socialLink->followers > 1 ? 'Followers' : 'Followers' }}
                                                            </td>
                                                        @endif
                                                        <td style="width: 10%;">
                                                            <?php
                                                            $newLivetreamPrice = 0;
                                                            $addNewLivetreamPrice = 0;

                                                            $user = App\Models\User::find($list->influencerdetails->user->id);

                                                            $fieldName = $list->advertising . '_price';
                                                            if ($user->advertisingMethodPrice != null) {
                                                                $newLivetreamPrice = $user->advertisingMethodPrice->$fieldName;
                                                            }
                                                            ?>
                                                            {{ number_format($row->current_price, 2) }} €
                                                        </td>
                                                        <td style="width: 30%; text-align:center;">
                                                            @if ($list->review == 1 && isset($list->influencer_request_accepts->rating_reviews))
                                                                <?php
                                                                $date = date('y-m-d', strtotime('-30 days'));
                                                                $reviewDate = date('y-m-d', strtotime($list->influencer_request_accepts->rating_reviews->created_at));
                                                                ?>
                                                                <div class="star-rating"
                                                                    style="font-size: 12px !important;"
                                                                    @if ($date < $reviewDate) class="stars" data-bs-toggle="modal" data-bs-target="#reviewRatingPopup{{ $list->id }}" @else class="stars disabled" data-bs-toggle="modal" data-bs-target="#reviewRatingPopup{{ $list->id }}" @endif>
                                                                    <?php
                                                                    $count = 5 - $list->influencer_request_accepts->rating_reviews->rating;
                                                                    ?>

                                                                    @for ($i = 0; $i < $list->influencer_request_accepts->rating_reviews->rating; $i++)
                                                                        <span class="star filled">★</span>
                                                                    @endfor

                                                                    @for ($j = 0; $j < $count; $j++)
                                                                        <span>★</span>
                                                                    @endfor
                                                                </div>
                                                                <!--modal reviewRatingPopup   form -->
                                                                <div class="modal fade ratingPopup influncer"
                                                                    id="reviewRatingPopup{{ $list->id }}"
                                                                    data-bs-backdrop="static" data-bs-keyboard="false"
                                                                    tabindex="-1"
                                                                    aria-labelledby="reviewRatingPopupLabel"
                                                                    aria-hidden="true">
                                                                    <div class="modal-dialog modal-lg">
                                                                        <div class="modal-content">
                                                                            <div class="modal-body">
                                                                                <button type="button" class="btn-close"
                                                                                    data-bs-dismiss="modal"
                                                                                    aria-label="Close"><img
                                                                                        src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg"
                                                                                        alt=""></button>
                                                                                <div
                                                                                    class="wizardHeading text-transform-normal">
                                                                                    Write a Review to Influencer</div>
                                                                                <div class="wizardForm mt-4">
                                                                                    <form method="post"
                                                                                        action="{{ url('/update-review') }}"
                                                                                        data-parsley-validate>
                                                                                        @csrf
                                                                                        <input type="hidden"
                                                                                            name="review_id"
                                                                                            @if (isset($list->influencer_request_accepts->rating_reviews)) value="{{ $list->influencer_request_accepts->rating_reviews->id }}" @endif>
                                                                                        <div
                                                                                            class="col-xl-12 col-md-12 col-12">
                                                                                            <div
                                                                                                class="form-group d-inline-block w-100">
                                                                                                <label>Overall rating</label>
                                                                                                <div class="rate">
                                                                                                    @php
                                                                                                        $date = date('y-m-d', strtotime('-30 days'));
                                                                                                        $reviewDate = date('y-m-d', strtotime($list->influencer_request_accepts->rating_reviews->created_at));
                                                                                                    @endphp
                                                                                                    @for ($i = 5; $i > 0; $i--)
                                                                                                        <input
                                                                                                            type="radio"
                                                                                                            @if ($date < $reviewDate) class="radioName" @else class="radioName disabled" @endif
                                                                                                            id="star{{ $i }}{{ $row->id }}one"
                                                                                                            name="rating"
                                                                                                            value="{{ $i }}"
                                                                                                            data-parsley-multiple="rating"
                                                                                                            @if (isset($list->influencer_request_accepts->rating_reviews) &&
                                                                                                                    $list->influencer_request_accepts->rating_reviews->rating == $i) checked @endif>
                                                                                                        <label
                                                                                                            for="star{{ $i }}{{ $row->id }}one"
                                                                                                            title="text">
                                                                                                        </label>
                                                                                                    @endfor
                                                                                                </div>
                                                                                            </div>
                                                                                            <div
                                                                                                class="col-xl-12 col-md-12 col-12">
                                                                                                <div class="form-group">
                                                                                                    <!-- Feedback -->
                                                                                                    <textarea id="review" name="review" @if ($date < $reviewDate) @else disabled @endif>
                                                                                                        @if (isset($list->influencer_request_accepts->rating_reviews))
                                                                                                        {{ $list->influencer_request_accepts->rating_reviews->review }}
                                                                                                        @endif
                                                                                                    </textarea>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                        <!-- Selected users //-->
                                                                                        <div class="popup2btns d-flex">
                                                                                            @if ($date < $reviewDate)
                                                                                                <input type="submit"
                                                                                                    class="et-submit"
                                                                                                    name="complete"
                                                                                                    value="Update Rating">
                                                                                            @else
                                                                                                <input type="submit"
                                                                                                    class="et-submit"
                                                                                                    name="complete"
                                                                                                    value="Update Rating"
                                                                                                    disabled>
                                                                                            @endif
                                                                                        </div>
                                                                                    </form>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <!--end  modal reviewRatingPopup   form -->
                                                            @else
                                                                <span>--</span>
                                                            @endif
                                                        </td>
                                                        <td class="actions" style="width: 25%; text-align:center;">
                                                            @if (isset($list->invoices->receipt))
                                                                <a href="{{ $list->invoices->receipt }}" target="_blank">
                                                                    <img src="{{ asset('/assets/front-end/images/icons/pdf-icon.svg') }}" style="width: 35px !important; height: 35px !important;">
                                                                </a>
                                                            @else
                                                            <img src="{{ asset('/assets/front-end/images/icons/pdf-icon.svg') }}" style="width: 35px !important; height: 35px !important;">
                                                            @endif

                                                            <button class="btn btn-campaign-history-brand-show-results"
                                                                style="font-size: 12px; margin:0; background-color: #fd9b8d; color: white; solid 1px #AD80FF"
                                                                target="popup"
                                                                data-bs-toggle="modal"
                                                                data-bs-target="#influencer-show-results-{{ $row->id }}">Show Results</button>
                                                        </td>
                                                        </tr>
                                                    @endforeach
                                                </table>
                                            </div>
                                            <div id="desktop-completed{{ $row->id }}" role="tabpanel"
                                                aria-labelledby="desktop-completed-tab{{ $row->id }}"
                                                style="margin: 0; max-width: 100% !important; font-size:12px !important;"
                                                class="container tab-pane">
                                                <table class="campaign-table">
                                                    @php $lists = App\Models\InfluencerRequestDetail::where('compaign_id',$row->compaign_id)->get(); @endphp
                                                    @foreach ($lists as $list)
                                                        {{-- TODO A temporary fix for badly/incomplete deletion of influencer data from admin panel  --}}
                                                        @if (empty($list->influencerdetails))
                                                            @php continue; @endphp
                                                        @endif
                                                        @if ($list->finish == 1 && $list->refund_reason == '')
                                                            <tr class="completed">
                                                                @if ($list->finish == 1 && $list->refund_reason == '')
                                                                    <td style="width: 10%; text-align:center;">
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/new/ph_complete.svg"
                                                                            alt="">
                                                                    </td>
                                                                @else
                                                                    <td style="width: 10%; text-align:center;">
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/new/ph_cancel.svg"
                                                                            alt="">
                                                                    </td>
                                                                @endif


                                                                @php
                                                                    $socialLink = App\Models\SocialConnect::where(
                                                                        'user_id',
                                                                        $list->influencerdetails->user->id,
                                                                    )
                                                                        ->where('media', $row->media)
                                                                        ->first();
                                                                @endphp
                                                                @if ($socialLink != '')
                                                                    <td style="width: 25%;">
                                                                        <img src="{{ url('/storage/app/' . $socialLink->picture) }}"
                                                                            alt="User">
                                                                        <a target="popup" data-bs-toggle="modal"
                                                                            data-bs-target="#influncerdetailpopup{{ $socialLink->id }}">@
                                                                            {{ isset($socialLink->name) ? $socialLink->name : $list->influencerdetails->user->first_name }}</a>
                                                                    </td>
                                                                    <td style="width: 15%;">
                                                                        {{ @$socialLink->followers }}
                                                                        {{ $socialLink->followers > 1 ? 'Followers' : 'Followers' }}
                                                                    </td>
                                                                @endif
                                                                <td style="width: 10%;">
                                                                    <?php
                                                                    $newLivetreamPrice = 0;
                                                                    $addNewLivetreamPrice = 0;

                                                                    $user = App\Models\User::find($list->influencerdetails->user->id);

                                                                    $fieldName = $list->advertising . '_price';
                                                                    if ($user->advertisingMethodPrice != null) {
                                                                        $newLivetreamPrice = $user->advertisingMethodPrice->$fieldName;
                                                                    }
                                                                    ?>
                                                                    {{ number_format($row->current_price, 2) }} €
                                                                </td>
                                                                <td style="width: 30%; text-align:center;">
                                                                    @if ($list->review == 1 && isset($list->influencer_request_accepts->rating_reviews))
                                                                        <?php
                                                                        $date = date('y-m-d', strtotime('-30 days'));
                                                                        $reviewDate = date('y-m-d', strtotime($list->influencer_request_accepts->rating_reviews->created_at));
                                                                        ?>
                                                                        <div class="star-rating"
                                                                            style="font-size: 12px !important;"
                                                                            @if ($date < $reviewDate) class="stars" data-bs-toggle="modal" data-bs-target="#reviewRatingPopup{{ $list->id }}" @else class="stars disabled" data-bs-toggle="modal" data-bs-target="#reviewRatingPopup{{ $list->id }}" @endif>
                                                                            <?php
                                                                            $count = 5 - $list->influencer_request_accepts->rating_reviews->rating;
                                                                            ?>

                                                                            @for ($i = 0; $i < $list->influencer_request_accepts->rating_reviews->rating; $i++)
                                                                                <span class="star filled">★</span>
                                                                            @endfor

                                                                            @for ($j = 0; $j < $count; $j++)
                                                                                <span>★</span>
                                                                            @endfor
                                                                        </div>
                                                                        <!--modal reviewRatingPopup   form -->
                                                                        <div class="modal fade ratingPopup influncer"
                                                                            id="reviewRatingPopup{{ $list->id }}"
                                                                            data-bs-backdrop="static"
                                                                            data-bs-keyboard="false" tabindex="-1"
                                                                            aria-labelledby="reviewRatingPopupLabel"
                                                                            aria-hidden="true">
                                                                            <div class="modal-dialog modal-lg"
                                                                                style="max-width:800px;">
                                                                                <div class="modal-content">
                                                                                    <div class="modal-body">
                                                                                        <button type="button"
                                                                                            class="btn-close"
                                                                                            data-bs-dismiss="modal"
                                                                                            aria-label="Close"><img
                                                                                                src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg"
                                                                                                alt=""></button>
                                                                                        <div
                                                                                            class="wizardHeading text-transform-normal">
                                                                                            Write a Review to Influencer
                                                                                        </div>
                                                                                        <div class="wizardForm mt-4">
                                                                                            <form method="post"
                                                                                                action="{{ url('/update-review') }}"
                                                                                                data-parsley-validate>
                                                                                                @csrf
                                                                                                <input type="hidden"
                                                                                                    name="review_id"
                                                                                                    @if (isset($list->influencer_request_accepts->rating_reviews)) value="{{ $list->influencer_request_accepts->rating_reviews->id }}" @endif>
                                                                                                <div
                                                                                                    class="col-xl-12 col-md-12 col-12">
                                                                                                    <div
                                                                                                        class="form-group d-inline-block w-100">
                                                                                                        <label>Overall
                                                                                                            rating</label>
                                                                                                        <div
                                                                                                            class="rate">
                                                                                                            <?php
                                                                                                            $date = date('y-m-d', strtotime('-30 days'));
                                                                                                            $reviewDate = date('y-m-d', strtotime($list->influencer_request_accepts->rating_reviews->created_at));
                                                                                                            ?>
                                                                                                            @for ($i = 5; $i > 0; $i--)
                                                                                                                <input
                                                                                                                    type="radio"
                                                                                                                    @if ($date < $reviewDate) class="radioName" @else class="radioName disabled" @endif
                                                                                                                    id="star{{ $i }}{{ $row->id }}one"
                                                                                                                    name="rating"
                                                                                                                    value="{{ $i }}"
                                                                                                                    data-parsley-multiple="rating"
                                                                                                                    @if (isset($list->influencer_request_accepts->rating_reviews) &&
                                                                                                                            $list->influencer_request_accepts->rating_reviews->rating == $i) checked @endif>
                                                                                                                <label
                                                                                                                    for="star{{ $i }}{{ $row->id }}one"
                                                                                                                    title="text"></label>
                                                                                                            @endfor
                                                                                                        </div>
                                                                                                    </div>
                                                                                                    <div
                                                                                                        class="col-xl-12 col-md-12 col-12">
                                                                                                        <div
                                                                                                            class="form-group">
                                                                                                            <!-- Feedback -->
                                                                                                            <textarea id="review" name="review" @if ($date >= $reviewDate) disabled @endif>
                                                                                                                @if (isset($list->influencer_request_accepts->rating_reviews))
                                                                                                                {{ $list->influencer_request_accepts->rating_reviews->review }}
                                                                                                                @endif
                                                                                                            </textarea>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                </div>
                                                                                                <!-- Selected users //-->
                                                                                                <div
                                                                                                    class="popup2btns d-flex">
                                                                                                    @if ($date < $reviewDate)
                                                                                                        <input
                                                                                                            type="submit"
                                                                                                            class="et-submit"
                                                                                                            name="complete"
                                                                                                            value="Update Rating">
                                                                                                    @else
                                                                                                        <input
                                                                                                            type="submit"
                                                                                                            class="et-submit"
                                                                                                            name="complete"
                                                                                                            value="Update Rating"
                                                                                                            disabled>
                                                                                                    @endif


                                                                                                </div>
                                                                                            </form>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <!--end  modal reviewRatingPopup   form -->
                                                                    @else
                                                                        <span>--</span>
                                                                    @endif
                                                                </td>
                                                                <td class="actions" style="width: 25%; text-align:center;">
                                                                    @if (isset($list->invoices->receipt))
                                                                        <a href="{{ $list->invoices->receipt }}" target="_blank">
                                                                            <img src="{{ asset('/assets/front-end/images/icons/pdf-icon.svg') }}" style="width: 35px !important; height: 35px !important;">
                                                                        </a>
                                                                    @else
                                                                    <img src="{{ asset('/assets/front-end/images/icons/pdf-icon.svg') }}" style="width: 35px !important; height: 35px !important;">
                                                                    @endif

                                                                    <button class="btn btn-campaign-history-brand-show-results"
                                                                        style="font-size: 12px; margin:0; background-color: #fd9b8d; color: white; solid 1px #AD80FF"
                                                                        style="height: 34px;" target="popup"
                                                                        data-bs-toggle="modal"
                                                                        data-bs-target="#influencer-show-results-{{ $row->id }}">Show Results</button>
                                                                </td>
                                                            </tr>
                                                            <!--modal reviewRatingPopupCompleted   form -->
                                                            <div class="modal fade ratingPopup influncer"
                                                                id="reviewRatingPopupCompleted{{ $list->id }}"
                                                                data-bs-backdrop="static" data-bs-keyboard="false"
                                                                tabindex="-1" aria-labelledby="reviewRatingPopupLabel"
                                                                aria-hidden="true">
                                                                <div class="modal-dialog modal-lg"
                                                                    style="max-width:800px;">
                                                                    <div class="modal-content">
                                                                        <div class="modal-body">
                                                                            <button type="button" class="btn-close"
                                                                                data-bs-dismiss="modal"
                                                                                aria-label="Close"><img
                                                                                    src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg"
                                                                                    alt=""></button>
                                                                            <div
                                                                                class="wizardHeading text-transform-normal">
                                                                                Write a Review to Influencer
                                                                            </div>
                                                                            <div class="wizardForm mt-4">
                                                                                <form method="post"
                                                                                    action="{{ url('/update-review') }}"
                                                                                    data-parsley-validate>
                                                                                    @csrf
                                                                                    <input type="hidden" name="review_id"
                                                                                        @if (isset($list->influencer_request_accepts->rating_reviews)) value="{{ $list->influencer_request_accepts->rating_reviews->id }}" @endif>
                                                                                    <div
                                                                                        class="col-xl-12 col-md-12 col-12">
                                                                                        <div
                                                                                            class="form-group d-inline-block w-100">
                                                                                            <label>Overall rating</label>
                                                                                            <div class="rate">
                                                                                                @for ($i = 5; $i > 0; $i--)
                                                                                                    <input type="radio"
                                                                                                        class="radioName"
                                                                                                        id="star{{ $i }}{{ $row->id }}two"
                                                                                                        name="rating"
                                                                                                        value="{{ $i }}"
                                                                                                        data-parsley-multiple="rating"
                                                                                                        @if (isset($list->influencer_request_accepts->rating_reviews) &&
                                                                                                                $list->influencer_request_accepts->rating_reviews->rating == $i) checked @endif>
                                                                                                    <label
                                                                                                        for="star{{ $i }}{{ $row->id }}two"
                                                                                                        title="text"></label>
                                                                                                @endfor
                                                                                            </div>
                                                                                        </div>
                                                                                        <div
                                                                                            class="col-xl-12 col-md-12 col-12">
                                                                                            <div class="form-group">
                                                                                                <!-- Feedback -->
                                                                                                <textarea id="review" name="review">
                                                                                                    @if (isset($list->influencer_request_accepts->rating_reviews))
                                                                                                    {{ $list->influencer_request_accepts->rating_reviews->review }}
                                                                                                    @endif
                                                                                                </textarea>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                    <!-- Selected users //-->
                                                                                    <div class="popup2btns d-flex">
                                                                                        <input type="submit"
                                                                                            class="et-submit"
                                                                                            name="complete"
                                                                                            value="Complete Campaign">
                                                                                    </div>
                                                                                </form>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <!--end  modal reviewRatingPopup   form -->
                                                            @if ($list->influencer_request_accepts)
                                                                <div class="modal fade confirm-submit influncer"
                                                                    id="reviewRating{{ $list->id }}"
                                                                    data-bs-backdrop="static" data-bs-keyboard="false"
                                                                    tabindex="-1" aria-labelledby="reviewRatingLabel"
                                                                    aria-hidden="true">
                                                                    <div class="modal-dialog modal-lg">
                                                                        <div class="modal-content">
                                                                            <div class="modal-body pt-4">
                                                                                <button type="button" class="btn-close"
                                                                                    data-bs-dismiss="modal"
                                                                                    aria-label="Close">
                                                                                    <img src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg" alt="">
                                                                                </button>
                                                                                <div class="wizardForm">
                                                                                    <input type="hidden"
                                                                                        name="influencer_request_accept_id"
                                                                                        id="influencer_request_accept_id"
                                                                                        value="{{ $list->influencer_request_accepts->id }}">
                                                                                    <div
                                                                                        class="col-xl-12 col-md-12 col-12 campHistory">
                                                                                        @include('front-user.pages.reuqest-review')
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="modal fade wewPopup influncer requesttime"
                                                                    id="complaintPopup{{ $list->id }}"
                                                                    data-bs-backdrop="static" data-bs-keyboard="false"
                                                                    tabindex="-1"
                                                                    aria-labelledby="reviewRatingPopupLabel"
                                                                    aria-hidden="true">
                                                                    <div class="modal-dialog modal-lg">
                                                                        <div class="modal-content">
                                                                            <div class="modal-body complaint-popup">
                                                                                <button type="button" class="btn-close"
                                                                                    data-bs-dismiss="modal"
                                                                                    aria-label="Close"><img
                                                                                        src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg"
                                                                                        alt=""></button>
                                                                                <div class="wizardHeading">Are you sure to
                                                                                    complaint?</div>

                                                                                <div class="wizardForm">
                                                                                    @csrf
                                                                                    <input type="hidden"
                                                                                        name="influencer_request_accept_id"
                                                                                        id="influencer_request_accept_id{{ $list->id }}"
                                                                                        value="{{ $list->influencer_request_accepts->id }}">
                                                                                    <div
                                                                                        class="col-xl-12 col-md-12 col-12">
                                                                                        <div class="widthBtnPopup">

                                                                                            <div
                                                                                                class="col-xl-12 col-md-12 col-12">
                                                                                                <div
                                                                                                    class="font-size-16  text-center">
                                                                                                    The complaint is
                                                                                                    immediately forwarded to
                                                                                                    the
                                                                                                    influencer concerned,
                                                                                                    who is then given
                                                                                                    seven
                                                                                                    days to respond. After
                                                                                                    we have received and
                                                                                                    analysed their feedback,
                                                                                                    we make a final
                                                                                                    decision.
                                                                                                </div>

                                                                                            </div>
                                                                                        </div>
                                                                                        <!-- Selected users //-->
                                                                                    </div>
                                                                                    <div
                                                                                        class="widthBtnPopup d-flex mt-4 mb-2 pt-2">
                                                                                        <a href="#"
                                                                                            class="et-submit red-btn mx-4 complant-btn popBackBtn"
                                                                                            data-bs-dismiss="modal"
                                                                                            aria-label="Close">Cancel</a>
                                                                                        <input
                                                                                            class="et-submit mx-4 popProcess"
                                                                                            type="button" name="proceed"
                                                                                            data-bs-target="#contact-review-popup{{ $list->id }}"
                                                                                            data-bs-toggle="modal"
                                                                                            data-bs-dismiss="modal"
                                                                                            value="Yes">
                                                                                    </div>
                                                                                    </form>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>

                                                                    <div class="modal fade confirm-content influncer"
                                                                        id="contact-review-popup{{ $list->id }}"
                                                                        data-bs-backdrop="static" data-bs-keyboard="false"
                                                                        tabindex="-1"
                                                                        aria-labelledby="requestSubmit5Label"
                                                                        aria-modal="true" role="dialog">
                                                                        <div class="modal-dialog modal-lg">
                                                                            <div class="modal-content">
                                                                                <div class="modal-body">
                                                                                    <button type="button"
                                                                                        class="btn-close"
                                                                                        data-bs-dismiss="modal"
                                                                                        aria-label="Close"><img
                                                                                            src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg"
                                                                                            alt=""></button>
                                                                                    <div class="wizardHeading">Complaint
                                                                                    </div>
                                                                                    <div class="contact-support">
                                                                                        <div
                                                                                            class="text-center wizardHeading-subheading">
                                                                                            Please
                                                                                            explain why you are not
                                                                                            satisfied with the
                                                                                            influencer’s
                                                                                            work:</div>
                                                                                        <form
                                                                                            action="{{ url('submit-complaint') }}"
                                                                                            method="Post"
                                                                                            enctype="multipart/form-data"
                                                                                            id="complaintForm{{ $list->id }}"
                                                                                            data-parsley-validate=""
                                                                                            novalidate="">
                                                                                            @csrf
                                                                                            <input type="hidden"
                                                                                                name="influencer_request_accept_id"
                                                                                                id="influencer_request_accept_id{{ $list->id }}"
                                                                                                value="{{ $list->influencer_request_accepts->id }}">
                                                                                            <input type="hidden"
                                                                                                name="page"
                                                                                                value="order">
                                                                                            <div class="form-group">
                                                                                                <label for=""
                                                                                                    class="form-label"></label>
                                                                                                <textarea class="form-control" name="comment" id="comment{{ $list->id }}" rows="3"
                                                                                                    placeholder="Please describe your problem" required="" data-parsley-required-message="Please enter problem."
                                                                                                    spellcheck="false" data-ms-editor="true"></textarea>
                                                                                            </div>
                                                                                            <div
                                                                                                class="form-group uploadFile">
                                                                                                <div
                                                                                                    class="custom-file-picker">
                                                                                                    <div
                                                                                                        class="picture-container form-group">
                                                                                                        <div
                                                                                                            class="picture">
                                                                                                            <span
                                                                                                                class="icon"
                                                                                                                id="icon">
                                                                                                                <div
                                                                                                                    class="smaltext">
                                                                                                                    Browse
                                                                                                                </div>
                                                                                                                <div
                                                                                                                    class="bigtext">
                                                                                                                    Or Drag
                                                                                                                    and
                                                                                                                    Drop to
                                                                                                                    Upload
                                                                                                                </div>
                                                                                                            </span>
                                                                                                            <input
                                                                                                                type="file"
                                                                                                                class="wizard-file ds"
                                                                                                                name="file"
                                                                                                                id="complaintFile{{ $list->id }}">
                                                                                                            <svg version="1.1"
                                                                                                                xmlns="http://www.w3.org/2000/svg"
                                                                                                                xmlns:xlink="http://www.w3.org/1999/xlink"
                                                                                                                x="0px"
                                                                                                                y="0px"
                                                                                                                viewBox="0 0 37 37"
                                                                                                                xml:space="preserve">
                                                                                                                <path
                                                                                                                    class="circ path"
                                                                                                                    style="fill:none;stroke:#77d27b;stroke-width:3;stroke-linejoin:round;stroke-miterlimit:10;"
                                                                                                                    d="M30.5,6.5L30.5,6.5c6.6,6.6,6.6,17.4,0,24l0,0c-6.6,6.6-17.4,6.6-24,0l0,0c-6.6-6.6-6.6-17.4,0-24l0,0C13.1-0.2,23.9-0.2,30.5,6.5z">
                                                                                                                </path>
                                                                                                                <polyline
                                                                                                                    class="tick path"
                                                                                                                    style="fill:none;stroke:#77d27b;stroke-width:3;stroke-linejoin:round;stroke-miterlimit:10;"
                                                                                                                    points="11.6,20 15.9,24.2 26.4,13.8 ">
                                                                                                                </polyline>
                                                                                                            </svg>
                                                                                                            <div
                                                                                                                class="popover-container text-center show-file">
                                                                                                                <p data-toggle="popover"
                                                                                                                    data-id="complaintFile{{ $list->id }}"
                                                                                                                    class="btn-popover"
                                                                                                                    data-original-title=""
                                                                                                                    title=""
                                                                                                                    data-bs-original-title="">
                                                                                                                    <span
                                                                                                                        class="file-total-viewer">0</span>
                                                                                                                    Files
                                                                                                                    Selected
                                                                                                                    <br /><input
                                                                                                                        type="button"
                                                                                                                        value="view"
                                                                                                                        href="javascript:void(0)"
                                                                                                                        class="btn btn-success btn-xs btn-file-view ds">
                                                                                                                </p>
                                                                                                            </div>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                            <div
                                                                                                class="extra-time-content-bs">

                                                                                                <a href="#"
                                                                                                    class="et-submit red-btn mx-4 complant-btn popBackBtn"
                                                                                                    data-bs-dismiss="modal"
                                                                                                    aria-label="Close">Cancel</a>
                                                                                                <input type="submit"
                                                                                                    class="et-submit ds"
                                                                                                    name="confirm"
                                                                                                    value="Send complaint">
                                                                                            </div>
                                                                                        </form>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                            @endif
                                                        @endif
                                                    @endforeach
                                                </table>
                                            </div>
                                            <div id="desktop-cancelled{{ $row->id }}" role="tabpanel"
                                                aria-labelledby="desktop-cancelled-tab{{ $row->id }}"
                                                style="margin: 0; max-width: 100% !important; font-size:12px !important;"
                                                class="container tab-pane">
                                                <table class="campaign-table">
                                                    @php $lists = App\Models\InfluencerRequestDetail::where('compaign_id',$row->compaign_id)->get(); @endphp
                                                    @foreach ($lists as $list)
                                                        {{-- TODO A temporary fix for badly/incomplete deletion of influencer data from admin panel  --}}
                                                        @if (empty($list->influencerdetails))
                                                            @php continue; @endphp
                                                        @endif
                                                        @if (!($list->finish == 1 && $list->refund_reason == ''))
                                                            <tr class="cancelled">
                                                                @if ($list->finish == 1 && $list->refund_reason == '')
                                                                    <td style="width: 10%; text-align:center;">
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/new/ph_complete.svg"
                                                                            alt="">
                                                                    </td>
                                                                @else
                                                                    <td style="width: 10%; text-align:center;">
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/new/ph_cancel.svg"
                                                                            alt="">
                                                                    </td>
                                                                @endif


                                                                @php
                                                                    $socialLink = App\Models\SocialConnect::where(
                                                                        'user_id',
                                                                        $list->influencerdetails->user->id,
                                                                    )
                                                                    ->where('media', $row->media)
                                                                    ->first();
                                                                @endphp
                                                                @if ($socialLink != '')
                                                                    <td style="width: 25%;">
                                                                        <img src="{{ url('/storage/app/' . $socialLink->picture) }}"
                                                                            alt="User">
                                                                        <a target="popup" data-bs-toggle="modal"
                                                                            data-bs-target="#influncerdetailpopup{{ $socialLink->id }}">@
                                                                            {{ isset($socialLink->name) ? $socialLink->name : $list->influencerdetails->user->first_name }}</a>
                                                                    </td>
                                                                    <td style="width: 15%;">
                                                                        {{ @$socialLink->followers }}
                                                                        {{ $socialLink->followers > 1 ? 'Followers' : 'Followers' }}
                                                                    </td>
                                                                @endif
                                                                <td style="width: 10%;">
                                                                    <?php
                                                                    $newLivetreamPrice = 0;
                                                                    $addNewLivetreamPrice = 0;

                                                                    $user = App\Models\User::find($list->influencerdetails->user->id);

                                                                    $fieldName = $list->advertising . '_price';
                                                                    if ($user->advertisingMethodPrice != null) {
                                                                        $newLivetreamPrice = $user->advertisingMethodPrice->$fieldName;
                                                                    }
                                                                    ?>
                                                                    {{ number_format($row->current_price, 2) }} €
                                                                </td>
                                                                <td style="width:30%; text-align:center;">
                                                                    @if ($list->review == 1 && isset($list->influencer_request_accepts->rating_reviews))
                                                                        <?php
                                                                        $date = date('y-m-d', strtotime('-30 days'));
                                                                        $reviewDate = date('y-m-d', strtotime($list->influencer_request_accepts->rating_reviews->created_at));
                                                                        ?>
                                                                        <div class="star-rating" class="star-rating"
                                                                            style="font-size: 12px !important;"
                                                                            @if ($date < $reviewDate) class="stars" data-bs-toggle="modal" data-bs-target="#reviewRatingPopup{{ $list->id }}" @else class="stars disabled" data-bs-toggle="modal" data-bs-target="#reviewRatingPopup{{ $list->id }}" @endif>
                                                                            <?php
                                                                            $count = 5 - $list->influencer_request_accepts->rating_reviews->rating;
                                                                            ?>

                                                                            @for ($i = 0; $i < $list->influencer_request_accepts->rating_reviews->rating; $i++)
                                                                                <span class="star filled">★</span>
                                                                            @endfor

                                                                            @for ($j = 0; $j < $count; $j++)
                                                                                <span>★</span>
                                                                            @endfor
                                                                        </div>
                                                                        <!--modal reviewRatingPopup   form -->
                                                                        <div class="modal fade ratingPopup influncer"
                                                                            id="reviewRatingPopup{{ $list->id }}"
                                                                            data-bs-backdrop="static"
                                                                            data-bs-keyboard="false" tabindex="-1"
                                                                            aria-labelledby="reviewRatingPopupLabel"
                                                                            aria-hidden="true">
                                                                            <div class="modal-dialog modal-lg"
                                                                                style="max-width:800px;">
                                                                                <div class="modal-content">
                                                                                    <div class="modal-body">
                                                                                        <button type="button"
                                                                                            class="btn-close"
                                                                                            data-bs-dismiss="modal"
                                                                                            aria-label="Close"><img
                                                                                                src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg"
                                                                                                alt=""></button>
                                                                                        <div
                                                                                            class="wizardHeading text-transform-normal">
                                                                                            Write a Review
                                                                                            to Influencer</div>
                                                                                        <div class="wizardForm mt-4">
                                                                                            <form method="post"
                                                                                                action="{{ url('/update-review') }}"
                                                                                                data-parsley-validate>
                                                                                                @csrf
                                                                                                <input type="hidden"
                                                                                                    name="review_id"
                                                                                                    @if (isset($list->influencer_request_accepts->rating_reviews)) value="{{ $list->influencer_request_accepts->rating_reviews->id }}" @endif>
                                                                                                <div
                                                                                                    class="col-xl-12 col-md-12 col-12">
                                                                                                    <div
                                                                                                        class="form-group d-inline-block w-100">
                                                                                                        <label>Overall
                                                                                                            rating</label>
                                                                                                        <div
                                                                                                            class="rate">
                                                                                                            <?php
                                                                                                            $date = date('y-m-d', strtotime('-30 days'));
                                                                                                            $reviewDate = date('y-m-d', strtotime($list->influencer_request_accepts->rating_reviews->created_at));
                                                                                                            ?>
                                                                                                            @for ($i = 5; $i > 0; $i--)
                                                                                                                <input
                                                                                                                    type="radio"
                                                                                                                    @if ($date < $reviewDate) class="radioName" @else class="radioName disabled" @endif
                                                                                                                    id="star{{ $i }}{{ $row->id }}one"
                                                                                                                    name="rating"
                                                                                                                    value="{{ $i }}"
                                                                                                                    data-parsley-multiple="rating"
                                                                                                                    @if (isset($list->influencer_request_accepts->rating_reviews) &&
                                                                                                                            $list->influencer_request_accepts->rating_reviews->rating == $i) checked @endif>
                                                                                                                <label
                                                                                                                    for="star{{ $i }}{{ $row->id }}one"
                                                                                                                    title="text"></label>
                                                                                                            @endfor
                                                                                                        </div>
                                                                                                    </div>
                                                                                                    <div
                                                                                                        class="col-xl-12 col-md-12 col-12">
                                                                                                        <div
                                                                                                            class="form-group">
                                                                                                            <!-- Feedback -->
                                                                                                            <textarea id="review" name="review" @if ($date >= $reviewDate) disabled @endif>
                                                                                                                @if (isset($list->influencer_request_accepts->rating_reviews))
                                                                                                                {{ $list->influencer_request_accepts->rating_reviews->review }}
                                                                                                                @endif
                                                                                                            </textarea>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                </div>
                                                                                                <!-- Selected users //-->
                                                                                                <div
                                                                                                    class="popup2btns d-flex">
                                                                                                    @if ($date < $reviewDate)
                                                                                                        <input
                                                                                                            type="submit"
                                                                                                            class="et-submit"
                                                                                                            name="complete"
                                                                                                            value="Update Rating">
                                                                                                    @else
                                                                                                        <input
                                                                                                            type="submit"
                                                                                                            class="et-submit"
                                                                                                            name="complete"
                                                                                                            value="Update Rating"
                                                                                                            disabled>
                                                                                                    @endif


                                                                                                </div>
                                                                                            </form>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <!--end  modal reviewRatingPopup   form -->
                                                                    @else
                                                                        <span>--</span>
                                                                    @endif
                                                                </td>
                                                                <td class="actions" style="width: 25%; text-align:center;">
                                                                    @if (isset($list->invoices->receipt))
                                                                        <a href="{{ $list->invoices->receipt }}" target="_blank">
                                                                            <img src="{{ asset('/assets/front-end/images/icons/pdf-icon.svg') }}" style="width: 35px !important; height: 35px !important;">
                                                                        </a>
                                                                    @else
                                                                    <img src="{{ asset('/assets/front-end/images/icons/pdf-icon.svg') }}" style="width: 35px !important; height: 35px !important;">
                                                                    @endif

                                                                    <button class="btn btn-campaign-history-brand-show-results"
                                                                        style="font-size: 12px; margin:0; background-color: #fd9b8d; color: white; solid 1px #AD80FF"
                                                                        style="height: 34px;" target="popup"
                                                                        data-bs-toggle="modal"
                                                                        data-bs-target="#influencer-show-results-{{ $row->id }}">Show Results</button>
                                                                </td>
                                                            </tr>
                                                            <!--modal reviewRatingPopup   form -->
                                                            <div class="modal fade ratingPopup influncer"
                                                                id="reviewRatingPopupCancelled{{ $list->id }}"
                                                                data-bs-backdrop="static" data-bs-keyboard="false"
                                                                tabindex="-1" aria-labelledby="reviewRatingPopupLabel"
                                                                aria-hidden="true">
                                                                <div class="modal-dialog modal-lg"
                                                                    style="max-width:800px;">
                                                                    <div class="modal-content">
                                                                        <div class="modal-body">
                                                                            <button type="button" class="btn-close"
                                                                                data-bs-dismiss="modal"
                                                                                aria-label="Close"><img
                                                                                    src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg"
                                                                                    alt=""></button>
                                                                            <div
                                                                                class="wizardHeading text-transform-normal">
                                                                                Write a Review to
                                                                                Influencer</div>
                                                                            <div class="wizardForm mt-4">
                                                                                <form method="post"
                                                                                    action="{{ url('/update-review') }}"
                                                                                    data-parsley-validate>
                                                                                    @csrf
                                                                                    <input type="hidden" name="review_id"
                                                                                        @if (isset($list->influencer_request_accepts->rating_reviews)) value="{{ $list->influencer_request_accepts->rating_reviews->id }}" @endif>
                                                                                    <div
                                                                                        class="col-xl-12 col-md-12 col-12">
                                                                                        <div
                                                                                            class="form-group d-inline-block w-100">
                                                                                            <label>Overall rating</label>
                                                                                            <div class="rate">
                                                                                                @for ($i = 5; $i > 0; $i--)
                                                                                                    <input type="radio"
                                                                                                        class="radioName"
                                                                                                        id="star{{ $i }}{{ $row->id }}three"
                                                                                                        name="rating"
                                                                                                        value="{{ $i }}"
                                                                                                        data-parsley-multiple="rating"
                                                                                                        @if (isset($list->influencer_request_accepts->rating_reviews) &&
                                                                                                                $list->influencer_request_accepts->rating_reviews->rating == $i) checked @endif>
                                                                                                    <label
                                                                                                        for="star{{ $i }}{{ $row->id }}three"
                                                                                                        title="text"></label>
                                                                                                @endfor
                                                                                            </div>
                                                                                        </div>
                                                                                        <div
                                                                                            class="col-xl-12 col-md-12 col-12">
                                                                                            <div class="form-group">
                                                                                                <!-- Feedback -->
                                                                                                <textarea id="review" name="review">
                                                                                                    @if (isset($list->influencer_request_accepts->rating_reviews))
{{ $list->influencer_request_accepts->rating_reviews->review }}
@endif
                                                                                                </textarea>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                    <!-- Selected users //-->
                                                                                    <div class="popup2btns d-flex">
                                                                                        <input type="submit"
                                                                                            class="et-submit"
                                                                                            name="complete"
                                                                                            value="Complete Campaign">

                                                                                    </div>
                                                                                </form>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <!--end  modal reviewRatingPopup   form -->
                                                        @endif
                                                    @endforeach
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {{-- Mobile View --}}

                                <div class="campaign-card mobile-view" style="display: none">
                                    <div class="campaign-header">
                                        <div class="row" style="margin-top: -18px;margin-left:0px;margin-right:0px;">
                                            <span class="btn-finished-phase"
                                                style="width: 100%; padding: 5px;display: flex;justify-content: space-between;"><span
                                                    style="text-align: left;">Finished</span><span
                                                    style="text-align: right;">ID #
                                                    {{ $row->compaign_id }}</span></span><br>
                                        </div>
                                        <div class="header-section">
                                            <div class="row" style="padding-left:20px; width:100%">
                                                <div class="col-9">
                                                    <h4 class="row truncate-text" style="font-weight: 700;font-size: 15px; padding-top: 10px; margin-left: -14px;">
                                                        {{ $row->compaign_title }}
                                                    </h4>
                                                    <div class="row" style="width:100%">
                                                        <span class="col-auto"
                                                            style="font-size: 7px !important; padding: 0 5px 0 0;"
                                                            target="popup" data-bs-toggle="modal"
                                                            data-bs-target="#showInfluencers{{ $row->id }}">
                                                            <img width="15" height="15"
                                                                src="{{ asset('/') }}/assets/front-end/images/new/three_users.svg">
                                                            {{ $row->total_influencer_count }} Influencer
                                                        </span>
                                                        <span class="col-auto"
                                                            style="font-size: 7px !important; padding: 0 5px 0 0;">
                                                            <img width="15" height="15"
                                                                src="{{ asset('/') }}/assets/front-end/images/new/survey_camp.svg">
                                                            {{ $row->advertising }}
                                                        </span>
                                                        <span class="col-auto"
                                                            style="font-size: 7px !important; padding: 0 5px 0 0;">
                                                            <img src="{{ asset('/') }}/assets/front-end/images/icons/campaigns-{{ $row->media }}.svg"
                                                                width="15" height="15">
                                                            {{ $row->post_type }}
                                                        </span>
                                                    </div>
                                                    <div class="row" style="padding-top:5px;">
                                                        <span class="text-success"
                                                            style="font-size: 16px;font-weight: 700;padding-left: 11px;">€
                                                            {{ number_format($row->total_amount, 2) }}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="col-3"
                                                    style="display:flex; align-items:center; justify-content:center; flex-direction:column; padding: 0 0px; margin: 0 0px;">
                                                    <button class="btn  btn-show-details" data-id="{{ $row->id }}"
                                                        target="popup" data-bs-toggle="modal"
                                                        style="width: 100% !important; padding: 0; margin: 2px 0; font-size: 10px;"
                                                        data-bs-target="#requestForm{{ $row->id }}">
                                                        Show Details
                                                    </button>
                                                    @foreach ($platform_invoices as $platform_invoice)
                                                        @if (empty($platform_invoice->receipt))
                                                        @php continue; @endphp
                                                        @endif
                                                        <a class="btn btn-show-fee-invoice" href="{{ $platform_invoice->receipt }}" target="_blank"
                                                            style="width: 100% !important; padding: 0; margin: 2px 0; font-size: 10px; border: solid 1px #AD80FF; color:#FFFFFF; background-color: #AD80FF">
                                                            Fee Invoice
                                                        </a>
                                                        @php break; @endphp
                                                    @endforeach
                                                    <span class="circle camp-button dropdown-button collapsed"
                                                        style="width: 20px; height: 20px; margin-top: 5px;"
                                                        data-bs-toggle="collapse"
                                                        data-bs-target="#mobile-collapse{{ $row->compaign_id }}"
                                                        aria-expanded="false"
                                                        aria-controls="mobile-collapse{{ $row->compaign_id }}"
                                                        rowspan="2">
                                                        <i class="collapse-icon fas fa-chevron-up" data-toggle="collapse"
                                                            data-target="#mobile-campaignDetails"></i>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="mobile-collapse{{ $row->compaign_id }}"
                                        class="accordion-collapse collapse "
                                        aria-labelledby="mobile-heading{{ $row->compaign_id }}"
                                        data-bs-parent="#accordionExample" style="padding: 0px 10px;">
                                        <ul class="nav nav-tabs tabs row" id="mobile-myTab{{ $row->id }}"
                                            role="tablist" style="border: none !important;">
                                            <li class="nav-item col" style="margin: 0">
                                                <a class="nav-link active btn-campaign-status all" style="margin: 0;"
                                                    id="mobile-all-tab{{ $row->id }}" data-bs-toggle="tab"
                                                    data-bs-target="#mobile-all{{ $row->id }}" type="button"
                                                    role="tab" aria-controls="mobile-all{{ $row->id }}"
                                                    aria-selected="true">All</a>
                                            </li>
                                            <li class="nav-item col" style="margin: 0">
                                                <a class="nav-link btn-campaign-status completed" style="margin: 0;"
                                                    id="mobile-completed-tab{{ $row->id }}" data-bs-toggle="tab"
                                                    data-bs-target="#mobile-completed{{ $row->id }}" type="button"
                                                    role="tab" aria-controls="mobile-completed{{ $row->id }}"
                                                    aria-selected="false">Completed</a>
                                            </li>
                                            <li class="nav-item col" style="margin: 0">
                                                <a class="nav-link btn-campaign-status cancelled" style="margin: 0;"
                                                    id="mobile-cancelled-tab{{ $row->id }}" data-bs-toggle="tab"
                                                    data-bs-target="#mobile-cancelled{{ $row->id }}" type="button"
                                                    role="tab" aria-controls="mobile-cancelled{{ $row->id }}"
                                                    aria-selected="false">Cancelled</a>
                                            </li>
                                        </ul>

                                        <div class="tab-content">
                                            <div id="mobile-all{{ $row->id }}" role="tabpanel"
                                                aria-labelledby="mobile-all-tab{{ $row->id }}"
                                                style="margin: 0; padding: 0; max-width: 100% !important; font-size:12px !important;"
                                                class="container tab-pane active">
                                                <table class="campaign-table" style="margin: 0; width: 100%;">
                                                    @php $lists = App\Models\InfluencerRequestDetail::where('compaign_id',$row->compaign_id)->get(); @endphp
                                                    @foreach ($lists as $list)
                                                        {{-- TODO A temporary fix for badly/incomplete deletion of influencer data from admin panel  --}}
                                                        @if (empty($list->influencerdetails))
                                                            @php continue; @endphp
                                                        @endif
                                                        @if ($list->finish == 1 && $list->refund_reason == '')
                                                            <tr class="completed">
                                                            @else
                                                            <tr class="cancelled">
                                                        @endif

                                                        @if ($list->finish == 1 && $list->refund_reason == '')
                                                            <td
                                                                style="width: 7%; text-align:center; display: table-cell; padding:0">
                                                                <img src="{{ asset('/') }}/assets/front-end/images/new/ph_complete.svg" style="width: 15px; height: 15px"
                                                                    alt="">
                                                            </td>
                                                        @else
                                                            <td
                                                                style="width: 7%; text-align:center; display: table-cell; padding:0">
                                                                <img src="{{ asset('/') }}/assets/front-end/images/new/ph_cancel.svg" style="width: 15px; height: 15px"
                                                                    alt="">
                                                            </td>
                                                        @endif


                                                        @php
                                                            $socialLink = App\Models\SocialConnect::where(
                                                                'user_id',
                                                                $list->influencerdetails->user->id,
                                                            )
                                                                ->where('media', $row->media)
                                                                ->first();
                                                        @endphp
                                                        @if ($socialLink != '')
                                                            <td style="width: 25%; display: table-cell; font-size: 6px; padding:0;">
                                                                <img src="{{ url('/storage/app/' . $socialLink->picture) }}"  style="width: 15px; height: 15px"
                                                                    alt="User">
                                                                <a target="popup" data-bs-toggle="modal" style="font-size: 6px; padding:0;"
                                                                    data-bs-target="#influncerdetailpopup{{ $socialLink->id }}">@
                                                                    {{ isset($socialLink->name) ? $socialLink->name : $list->influencerdetails->user->first_name }}</a>
                                                            </td>
                                                            <td style="width: 13%; text-align:center; display: table-cell; font-size: 6px; padding:0;">
                                                                {{ @$socialLink->followers }} Follower
                                                            </td>
                                                        @endif
                                                        <td style="width: 6%; text-align:center; display: table-cell; font-size: 6px; padding:0;">
                                                            <?php
                                                            $newLivetreamPrice = 0;
                                                            $addNewLivetreamPrice = 0;

                                                            $user = App\Models\User::find($list->influencerdetails->user->id);

                                                            $fieldName = $list->advertising . '_price';
                                                            if ($user->advertisingMethodPrice != null) {
                                                                $newLivetreamPrice = $user->advertisingMethodPrice->$fieldName;
                                                            }
                                                            ?>
                                                            {{ number_format($row->current_price, 2) }} €
                                                        </td>
                                                        <td style="width: 25%; text-align:center; display: table-cell; font-size: 6px; padding:0;">
                                                            @if ($list->review == 1 && isset($list->influencer_request_accepts->rating_reviews))
                                                                <?php
                                                                $date = date('y-m-d', strtotime('-30 days'));
                                                                $reviewDate = date('y-m-d', strtotime($list->influencer_request_accepts->rating_reviews->created_at));
                                                                ?>
                                                                <div class="star-rating"
                                                                    style="font-size: 6px !important;"
                                                                    @if ($date < $reviewDate) class="stars" data-bs-toggle="modal" data-bs-target="#reviewRatingPopupMobile{{ $list->id }}" @else class="stars disabled" data-bs-toggle="modal" data-bs-target="#reviewRatingPopupMobile{{ $list->id }}" @endif>
                                                                    <?php
                                                                    $count = 5 - $list->influencer_request_accepts->rating_reviews->rating;
                                                                    ?>

                                                                    @for ($i = 0; $i < $list->influencer_request_accepts->rating_reviews->rating; $i++)
                                                                        <span class="star filled">★</span>
                                                                    @endfor

                                                                    @for ($j = 0; $j < $count; $j++)
                                                                        <span>★</span>
                                                                    @endfor
                                                                </div>
                                                                <!--modal reviewRatingPopup   form -->
                                                                <div class="modal fade ratingPopup influncer"
                                                                    id="reviewRatingPopupMobile{{ $list->id }}"
                                                                    data-bs-backdrop="static" data-bs-keyboard="false"
                                                                    tabindex="-1"
                                                                    aria-labelledby="reviewRatingPopupLabel"
                                                                    aria-hidden="true">
                                                                    <div class="modal-dialog modal-lg"
                                                                        style="max-width:800px;">
                                                                        <div class="modal-content">
                                                                            <div class="modal-body">
                                                                                <button type="button" class="btn-close"
                                                                                    data-bs-dismiss="modal"
                                                                                    aria-label="Close"><img
                                                                                        src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg"
                                                                                        alt=""></button>
                                                                                <div
                                                                                    class="wizardHeading text-transform-normal">
                                                                                    Write a Review to Influencer
                                                                                </div>
                                                                                <div class="wizardForm mt-4">
                                                                                    <form method="post"
                                                                                        action="{{ url('/update-review') }}"
                                                                                        data-parsley-validate>
                                                                                        @csrf
                                                                                        <input type="hidden"
                                                                                            name="review_id"
                                                                                            @if (isset($list->influencer_request_accepts->rating_reviews)) value="{{ $list->influencer_request_accepts->rating_reviews->id }}" @endif>
                                                                                        <div
                                                                                            class="col-xl-12 col-md-12 col-12">
                                                                                            <div
                                                                                                class="form-group d-inline-block w-100">
                                                                                                <label>Overall
                                                                                                    rating</label>
                                                                                                <div class="rate">
                                                                                                    <?php
                                                                                                    $date = date('y-m-d', strtotime('-30 days'));
                                                                                                    $reviewDate = date('y-m-d', strtotime($list->influencer_request_accepts->rating_reviews->created_at));
                                                                                                    ?>
                                                                                                    @for ($i = 5; $i > 0; $i--)
                                                                                                        <input
                                                                                                            type="radio"
                                                                                                            @if ($date < $reviewDate) class="radioName" @else class="radioName disabled" @endif
                                                                                                            id="star{{ $i }}{{ $row->id }}one"
                                                                                                            name="rating"
                                                                                                            value="{{ $i }}"
                                                                                                            data-parsley-multiple="rating"
                                                                                                            @if (isset($list->influencer_request_accepts->rating_reviews) &&
                                                                                                                    $list->influencer_request_accepts->rating_reviews->rating == $i) checked @endif>
                                                                                                        <label
                                                                                                            for="star{{ $i }}{{ $row->id }}one"
                                                                                                            title="text"></label>
                                                                                                    @endfor
                                                                                                </div>
                                                                                            </div>
                                                                                            <div
                                                                                                class="col-xl-12 col-md-12 col-12">
                                                                                                <div class="form-group">
                                                                                                    <!-- Feedback -->
                                                                                                    <textarea id="review" name="review" @if ($date >= $reviewDate) disabled @endif>
                                                                                                        @if (isset($list->influencer_request_accepts->rating_reviews))
                                                                                                        {{ $list->influencer_request_accepts->rating_reviews->review }}
                                                                                                    @endif
                                                                                                    </textarea>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                        <!-- Selected users //-->
                                                                                        <div class="popup2btns d-flex">
                                                                                            @if ($date < $reviewDate)
                                                                                                <input type="submit"
                                                                                                    class="et-submit"
                                                                                                    name="complete"
                                                                                                    value="Update Rating">
                                                                                            @else
                                                                                                <input type="submit"
                                                                                                    class="et-submit"
                                                                                                    name="complete"
                                                                                                    value="Update Rating"
                                                                                                    disabled>
                                                                                            @endif


                                                                                        </div>
                                                                                    </form>
                                                                                    <!-- <input type="button" class="et-submit ds" name="confirm" value="Send complaint"  data-bs-target="#complaintconfirm{{ $row->id }}" data-bs-toggle="modal" data-bs-dismiss="modal" value="Yes"> -->
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <!--end  modal reviewRatingPopup   form -->
                                                            @else
                                                                <span>--</span>
                                                            @endif
                                                        </td>
                                                        <td class="actions" style="width: 22%; text-align:center; display: table-cell; font-size: 6px; padding:0;">
                                                            @if (isset($list->invoices->receipt))
                                                                <a href="{{ $list->invoices->receipt }}" target="_blank">
                                                                    <img src="{{ asset('/assets/front-end/images/icons/pdf-icon.svg') }}" class="mobile-pdf-icon">
                                                                </a>
                                                            @else
                                                            <img src="{{ asset('/assets/front-end/images/icons/pdf-icon.svg') }}" class="mobile-pdf-icon">
                                                            @endif

                                                            <button class="btn btn-campaign-history-brand-show-results"
                                                                style="font-size: 6px; margin:0; padding: 2px; background-color: #fd9b8d; color: white; solid 1px #AD80FF; width: auto;"
                                                                target="popup"
                                                                data-bs-toggle="modal"
                                                                data-bs-target="#influencer-show-results-{{ $row->id }}">Show Results</button>
                                                        </td>

                                                        </tr>
                                                    @endforeach

                                                </table>
                                            </div>
                                            <div id="mobile-completed{{ $row->id }}" role="tabpanel"
                                                aria-labelledby="mobile-completed-tab{{ $row->id }}"
                                                style="margin: 0; padding: 0; max-width: 100% !important; font-size:12px !important;"
                                                class="container tab-pane">
                                                <table class="campaign-table" style="margin: 0; width: 100%;">
                                                    @php $lists = App\Models\InfluencerRequestDetail::where('compaign_id',$row->compaign_id)->get(); @endphp
                                                    @foreach ($lists as $list)
                                                        {{-- TODO A temporary fix for badly/incomplete deletion of influencer data from admin panel  --}}
                                                        @if (empty($list->influencerdetails))
                                                            @php continue; @endphp
                                                        @endif
                                                        @if ($list->finish == 1 && $list->refund_reason == '')
                                                            <tr class="completed">
                                                                @if ($list->finish == 1 && $list->refund_reason == '')
                                                                    <td
                                                                        style="width: 7%; text-align:center; display: table-cell; padding:0">
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/new/ph_complete.svg" style="width: 15px; height: 15px"
                                                                            alt="">
                                                                    </td>
                                                                @else
                                                                    <td
                                                                        style="width: 7%; text-align:center; display: table-cell; padding:0">
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/new/ph_cancel.svg" style="width: 15px; height: 15px"
                                                                            alt="">
                                                                    </td>
                                                                @endif


                                                                @php
                                                                    $socialLink = App\Models\SocialConnect::where(
                                                                        'user_id',
                                                                        $list->influencerdetails->user->id,
                                                                    )
                                                                        ->where('media', $row->media)
                                                                        ->first();
                                                                @endphp
                                                                @if ($socialLink != '')
                                                                    <td style="width: 25%; display: table-cell; font-size: 6px; padding:0;">
                                                                        <img src="{{ url('/storage/app/' . $socialLink->picture) }}" style="width: 15px; height: 15px"
                                                                            alt="User">
                                                                        <a target="popup" data-bs-toggle="modal" style="font-size: 6px; padding:0;"
                                                                            data-bs-target="#influncerdetailpopup{{ $socialLink->id }}">@
                                                                            {{ isset($socialLink->name) ? $socialLink->name : $list->influencerdetails->user->first_name }}</a>
                                                                    </td>
                                                                    <td style="width: 13%; text-align:center; display: table-cell; font-size: 6px; padding:0;">
                                                                        {{ @$socialLink->followers }} Follower
                                                                    </td>
                                                                @endif
                                                                <td style="width: 6%; text-align:center; display: table-cell; font-size: 6px; padding:0;">
                                                                    <?php
                                                                    $newLivetreamPrice = 0;
                                                                    $addNewLivetreamPrice = 0;

                                                                    $user = App\Models\User::find($list->influencerdetails->user->id);

                                                                    $fieldName = $list->advertising . '_price';
                                                                    if ($user->advertisingMethodPrice != null) {
                                                                        $newLivetreamPrice = $user->advertisingMethodPrice->$fieldName;
                                                                    }
                                                                    ?>
                                                                    {{ number_format($row->current_price, 2) }} €
                                                                </td>
                                                                <td style="width: 25%; text-align:center; display: table-cell; font-size: 6px; padding:0;">
                                                                    @if ($list->review == 1 && isset($list->influencer_request_accepts->rating_reviews))
                                                                        <?php
                                                                        $date = date('y-m-d', strtotime('-30 days'));
                                                                        $reviewDate = date('y-m-d', strtotime($list->influencer_request_accepts->rating_reviews->created_at));
                                                                        ?>
                                                                        <div class="star-rating"
                                                                            style="font-size: 6px !important;"
                                                                            @if ($date < $reviewDate) class="stars" data-bs-toggle="modal" data-bs-target="#reviewRatingPopupMobile{{ $list->id }}" @else class="stars disabled" data-bs-toggle="modal" data-bs-target="#reviewRatingPopupMobile{{ $list->id }}" @endif>
                                                                            <?php
                                                                            $count = 5 - $list->influencer_request_accepts->rating_reviews->rating;
                                                                            ?>

                                                                            @for ($i = 0; $i < $list->influencer_request_accepts->rating_reviews->rating; $i++)
                                                                                <span class="star filled">★</span>
                                                                            @endfor

                                                                            @for ($j = 0; $j < $count; $j++)
                                                                                <span>★</span>
                                                                            @endfor
                                                                        </div>
                                                                        <!--modal reviewRatingPopup   form -->
                                                                        <div class="modal fade ratingPopup influncer"
                                                                            id="reviewRatingPopupMobile{{ $list->id }}"
                                                                            data-bs-backdrop="static" data-bs-keyboard="false"
                                                                            tabindex="-1"
                                                                            aria-labelledby="reviewRatingPopupLabel"
                                                                            aria-hidden="true">
                                                                            <div class="modal-dialog modal-lg"
                                                                                style="max-width:800px;">
                                                                                <div class="modal-content">
                                                                                    <div class="modal-body">
                                                                                        <button type="button" class="btn-close"
                                                                                            data-bs-dismiss="modal"
                                                                                            aria-label="Close"><img
                                                                                                src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg"
                                                                                                alt=""></button>
                                                                                        <div
                                                                                            class="wizardHeading text-transform-normal">
                                                                                            Write a Review to Influencer
                                                                                        </div>
                                                                                        <div class="wizardForm mt-4">
                                                                                            <form method="post"
                                                                                                action="{{ url('/update-review') }}"
                                                                                                data-parsley-validate>
                                                                                                @csrf
                                                                                                <input type="hidden"
                                                                                                    name="review_id"
                                                                                                    @if (isset($list->influencer_request_accepts->rating_reviews)) value="{{ $list->influencer_request_accepts->rating_reviews->id }}" @endif>
                                                                                                <div
                                                                                                    class="col-xl-12 col-md-12 col-12">
                                                                                                    <div
                                                                                                        class="form-group d-inline-block w-100">
                                                                                                        <label>Overall
                                                                                                            rating</label>
                                                                                                        <div class="rate">
                                                                                                            <?php
                                                                                                            $date = date('y-m-d', strtotime('-30 days'));
                                                                                                            $reviewDate = date('y-m-d', strtotime($list->influencer_request_accepts->rating_reviews->created_at));
                                                                                                            ?>
                                                                                                            @for ($i = 5; $i > 0; $i--)
                                                                                                                <input
                                                                                                                    type="radio"
                                                                                                                    @if ($date < $reviewDate) class="radioName" @else class="radioName disabled" @endif
                                                                                                                    id="star{{ $i }}{{ $row->id }}one"
                                                                                                                    name="rating"
                                                                                                                    value="{{ $i }}"
                                                                                                                    data-parsley-multiple="rating"
                                                                                                                    @if (isset($list->influencer_request_accepts->rating_reviews) &&
                                                                                                                            $list->influencer_request_accepts->rating_reviews->rating == $i) checked @endif>
                                                                                                                <label
                                                                                                                    for="star{{ $i }}{{ $row->id }}one"
                                                                                                                    title="text"></label>
                                                                                                            @endfor
                                                                                                        </div>
                                                                                                    </div>
                                                                                                    <div
                                                                                                        class="col-xl-12 col-md-12 col-12">
                                                                                                        <div class="form-group">
                                                                                                            <!-- Feedback -->
                                                                                                            <textarea id="review" name="review" @if ($date >= $reviewDate) disabled @endif>
                                                                                                                @if (isset($list->influencer_request_accepts->rating_reviews))
                                                                                                                {{ $list->influencer_request_accepts->rating_reviews->review }}
                                                                                                            @endif
                                                                                                            </textarea>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                </div>
                                                                                                <!-- Selected users //-->
                                                                                                <div class="popup2btns d-flex">
                                                                                                    @if ($date < $reviewDate)
                                                                                                        <input type="submit"
                                                                                                            class="et-submit"
                                                                                                            name="complete"
                                                                                                            value="Update Rating">
                                                                                                    @else
                                                                                                        <input type="submit"
                                                                                                            class="et-submit"
                                                                                                            name="complete"
                                                                                                            value="Update Rating"
                                                                                                            disabled>
                                                                                                    @endif


                                                                                                </div>
                                                                                            </form>
                                                                                            <!-- <input type="button" class="et-submit ds" name="confirm" value="Send complaint"  data-bs-target="#complaintconfirm{{ $row->id }}" data-bs-toggle="modal" data-bs-dismiss="modal" value="Yes"> -->
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <!--end  modal reviewRatingPopup   form -->
                                                                    @else
                                                                        <span>--</span>
                                                                    @endif
                                                                </td>
                                                                <td class="actions" style="width: 22%; text-align:center; display: table-cell; font-size: 6px; padding:0;">
                                                                    @if (isset($list->invoices->receipt))
                                                                        <a href="{{ $list->invoices->receipt }}" target="_blank">
                                                                            <img src="{{ asset('/assets/front-end/images/icons/pdf-icon.svg') }}" class="mobile-pdf-icon">
                                                                        </a>
                                                                    @else
                                                                    <img src="{{ asset('/assets/front-end/images/icons/pdf-icon.svg') }}" class="mobile-pdf-icon">
                                                                    @endif

                                                                    <button class="btn btn-campaign-history-brand-show-results"
                                                                        style="font-size: 6px; margin:0; padding: 2px; background-color: #fd9b8d; color: white; solid 1px #AD80FF; width: auto;"
                                                                        target="popup"
                                                                        data-bs-toggle="modal"
                                                                        data-bs-target="#influencer-show-results-{{ $row->id }}">Show Results</button>
                                                                </td>
                                                            </tr>
                                                        @endif
                                                    @endforeach

                                                </table>
                                            </div>
                                            <div id="mobile-cancelled{{ $row->id }}" role="tabpanel"
                                                aria-labelledby="mobile-cancelled-tab{{ $row->id }}"
                                                style="margin: 0; padding: 0; max-width: 100% !important; font-size:12px !important;"
                                                class="container tab-pane">
                                                <table class="campaign-table" style="margin: 0; width: 100%;">
                                                    @php $lists = App\Models\InfluencerRequestDetail::where('compaign_id', $row->compaign_id)->get(); @endphp
                                                    @foreach ($lists as $list)
                                                        {{-- TODO A temporary fix for badly/incomplete deletion of influencer data from admin panel  --}}
                                                        @if (empty($list->influencerdetails))
                                                            @php continue; @endphp
                                                        @endif
                                                        @if (!($list->finish == 1 && $list->refund_reason == ''))
                                                            <tr class="cancelled">
                                                                @if ($list->finish == 1 && $list->refund_reason == '')
                                                                    <td
                                                                        style="width: 7%; text-align:center; display: table-cell; padding:0">
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/new/ph_complete.svg" style="width: 15px; height: 15px" alt="">
                                                                    </td>
                                                                @else
                                                                    <td style="width: 7%; text-align:center; display: table-cell; padding:0">
                                                                        <img src="{{ asset('/') }}/assets/front-end/images/new/ph_cancel.svg" style="width: 15px; height: 15px" alt="">
                                                                    </td>
                                                                @endif

                                                                @php
                                                                    $socialLink = App\Models\SocialConnect::where(
                                                                        'user_id',
                                                                        $list->influencerdetails->user->id,
                                                                    )
                                                                        ->where('media', $row->media)
                                                                        ->first();
                                                                @endphp
                                                                @if ($socialLink != '')
                                                                    <td style="width: 25%; display: table-cell; font-size: 6px; padding:0;">
                                                                        <img src="{{ url('/storage/app/' . $socialLink->picture) }}" style="width: 15px; height: 15px"
                                                                            alt="User">
                                                                        <a target="popup" data-bs-toggle="modal" style="font-size: 6px; padding:0;"
                                                                            data-bs-target="#influncerdetailpopup{{ $socialLink->id }}">@
                                                                            {{ isset($socialLink->name) ? $socialLink->name : $list->influencerdetails->user->first_name }}</a>
                                                                    </td>
                                                                    <td style="width: 13%; text-align:center; display: table-cell; font-size: 6px; padding:0;">
                                                                        {{ @$socialLink->followers }} Follower
                                                                    </td>
                                                                @endif
                                                                <td style="width: 6%; text-align:center; display: table-cell; font-size: 6px; padding:0;">
                                                                    <?php
                                                                    $newLivetreamPrice = 0;
                                                                    $addNewLivetreamPrice = 0;

                                                                    $user = App\Models\User::find($list->influencerdetails->user->id);

                                                                    $fieldName = $list->advertising . '_price';
                                                                    if ($user->advertisingMethodPrice != null) {
                                                                        $newLivetreamPrice = $user->advertisingMethodPrice->$fieldName;
                                                                    }
                                                                    ?>
                                                                    {{ number_format($row->current_price, 2) }} €
                                                                </td>
                                                                <td style="width: 25%; text-align:center; display: table-cell; font-size: 6px; padding:0;">
                                                                    @if ($list->review == 1 && isset($list->influencer_request_accepts->rating_reviews))
                                                                        <?php
                                                                        $date = date('y-m-d', strtotime('-30 days'));
                                                                        $reviewDate = date('y-m-d', strtotime($list->influencer_request_accepts->rating_reviews->created_at));
                                                                        ?>
                                                                        <div class="star-rating"
                                                                            style="font-size: 6px !important;"
                                                                            @if ($date < $reviewDate) class="stars" data-bs-toggle="modal" data-bs-target="#reviewRatingPopupMobile{{ $list->id }}" @else class="stars disabled" data-bs-toggle="modal" data-bs-target="#reviewRatingPopupMobile{{ $list->id }}" @endif>
                                                                            <?php
                                                                            $count = 5 - $list->influencer_request_accepts->rating_reviews->rating;
                                                                            ?>

                                                                            @for ($i = 0; $i < $list->influencer_request_accepts->rating_reviews->rating; $i++)
                                                                                <span class="star filled">★</span>
                                                                            @endfor

                                                                            @for ($j = 0; $j < $count; $j++)
                                                                                <span>★</span>
                                                                            @endfor
                                                                        </div>
                                                                        <!--modal reviewRatingPopup   form -->
                                                                        <div class="modal fade ratingPopup influncer"
                                                                            id="reviewRatingPopupMobile{{ $list->id }}"
                                                                            data-bs-backdrop="static" data-bs-keyboard="false"
                                                                            tabindex="-1"
                                                                            aria-labelledby="reviewRatingPopupLabel"
                                                                            aria-hidden="true">
                                                                            <div class="modal-dialog modal-lg"
                                                                                style="max-width:800px;">
                                                                                <div class="modal-content">
                                                                                    <div class="modal-body">
                                                                                        <button type="button" class="btn-close"
                                                                                            data-bs-dismiss="modal"
                                                                                            aria-label="Close"><img
                                                                                                src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg"
                                                                                                alt=""></button>
                                                                                        <div
                                                                                            class="wizardHeading text-transform-normal">
                                                                                            Write a Review to Influencer
                                                                                        </div>
                                                                                        <div class="wizardForm mt-4">
                                                                                            <form method="post"
                                                                                                action="{{ url('/update-review') }}"
                                                                                                data-parsley-validate>
                                                                                                @csrf
                                                                                                <input type="hidden"
                                                                                                    name="review_id"
                                                                                                    @if (isset($list->influencer_request_accepts->rating_reviews)) value="{{ $list->influencer_request_accepts->rating_reviews->id }}" @endif>
                                                                                                <div
                                                                                                    class="col-xl-12 col-md-12 col-12">
                                                                                                    <div
                                                                                                        class="form-group d-inline-block w-100">
                                                                                                        <label>Overall
                                                                                                            rating</label>
                                                                                                        <div class="rate">
                                                                                                            <?php
                                                                                                            $date = date('y-m-d', strtotime('-30 days'));
                                                                                                            $reviewDate = date('y-m-d', strtotime($list->influencer_request_accepts->rating_reviews->created_at));
                                                                                                            ?>
                                                                                                            @for ($i = 5; $i > 0; $i--)
                                                                                                                <input
                                                                                                                    type="radio"
                                                                                                                    @if ($date < $reviewDate) class="radioName" @else class="radioName disabled" @endif
                                                                                                                    id="star{{ $i }}{{ $row->id }}one"
                                                                                                                    name="rating"
                                                                                                                    value="{{ $i }}"
                                                                                                                    data-parsley-multiple="rating"
                                                                                                                    @if (isset($list->influencer_request_accepts->rating_reviews) &&
                                                                                                                            $list->influencer_request_accepts->rating_reviews->rating == $i) checked @endif>
                                                                                                                <label
                                                                                                                    for="star{{ $i }}{{ $row->id }}one"
                                                                                                                    title="text"></label>
                                                                                                            @endfor
                                                                                                        </div>
                                                                                                    </div>
                                                                                                    <div
                                                                                                        class="col-xl-12 col-md-12 col-12">
                                                                                                        <div class="form-group">
                                                                                                            <!-- Feedback -->
                                                                                                            <textarea id="review" name="review" @if ($date >= $reviewDate) disabled @endif>
                                                                                                                @if (isset($list->influencer_request_accepts->rating_reviews))
                                                                                                                {{ $list->influencer_request_accepts->rating_reviews->review }}
                                                                                                            @endif
                                                                                                            </textarea>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                </div>
                                                                                                <!-- Selected users //-->
                                                                                                <div class="popup2btns d-flex">
                                                                                                    @if ($date < $reviewDate)
                                                                                                        <input type="submit"
                                                                                                            class="et-submit"
                                                                                                            name="complete"
                                                                                                            value="Update Rating">
                                                                                                    @else
                                                                                                        <input type="submit"
                                                                                                            class="et-submit"
                                                                                                            name="complete"
                                                                                                            value="Update Rating"
                                                                                                            disabled>
                                                                                                    @endif


                                                                                                </div>
                                                                                            </form>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <!--end  modal reviewRatingPopup   form -->
                                                                    @else
                                                                        <span>--</span>
                                                                    @endif
                                                                </td>
                                                                <td class="actions" style="width: 22%; text-align:center; display: table-cell; font-size: 6px; padding:0;">
                                                                    @if (isset($list->invoices->receipt))
                                                                        <a href="{{ $list->invoices->receipt }}" target="_blank">
                                                                            <img src="{{ asset('/assets/front-end/images/icons/pdf-icon.svg') }}" class="mobile-pdf-icon">
                                                                        </a>
                                                                    @else
                                                                    <img src="{{ asset('/assets/front-end/images/icons/pdf-icon.svg') }}" class="mobile-pdf-icon">
                                                                    @endif

                                                                    <button class="btn btn-campaign-history-brand-show-results"
                                                                        style="font-size: 6px; margin:0; padding: 2px; background-color: #fd9b8d; color: white; solid 1px #AD80FF; width: auto;"
                                                                        target="popup"
                                                                        data-bs-toggle="modal"
                                                                        data-bs-target="#influencer-show-results-{{ $row->id }}">Show Results</button>
                                                                </td>
                                                            </tr>
                                                        @endif
                                                    @endforeach

                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <x-modals.influencer.influencer-show-results :influencer-request-detail="$row" />
                                
                                <div class="modal fade influncer wewPopup request-popup"
                                    id="requestForm{{ $row->id }}" data-bs-backdrop="static"
                                    data-bs-keyboard="false" tabindex="-1" aria-labelledby="requestFormLabel"
                                    aria-hidden="true">
                                    <div class="modal-dialog default-width modal-dialog-centered">
                                        <div class="modal-content">
                                            <div class="modal-body">
                                                <button type="button" class="btn-close" data-bs-dismiss="modal"
                                                    aria-label="Close">
                                                    <img src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg"
                                                        alt="">
                                                </button>
                                                <div class="popup-title">{{ $row->compaign_title }}</div>
                                                <div class="popup-title-id">
                                                    Campaign ID: {{ $row->compaign_id }}
                                                </div>
                                                <form method="post" id="requestFormSubmit{{ $row->id }}"
                                                    action="{{ url('/request-form') }}" data-parsley-validate>
                                                    @csrf
                                                    <input type="hidden" name="influencer_request_detail_id"
                                                        id="influencer_request_detail_id"
                                                        value="{{ isset($row->id) ? $row->id : '' }}">
                                                    <ul class="nav nav-tabs ordertab" id="myTab" role="tablist">
                                                        <li class="nav-item" role="presentation">
                                                            <button class="nav-link active"
                                                                id="general-information-tab{{ isset($row->id) ? $row->id : '' }}"
                                                                data-bs-toggle="tab"
                                                                data-bs-target="#general-information{{ isset($row->id) ? $row->id : '' }}"
                                                                type="button" role="tab"
                                                                aria-controls="general-information{{ isset($row->id) ? $row->id : '' }}"
                                                                aria-selected="true">General Information
                                                            </button>
                                                        </li>
                                                        <li class="nav-item" role="presentation">
                                                            <button class="nav-link"
                                                                id="order-detail-tab{{ isset($row->id) ? $row->id : '' }}"
                                                                data-bs-toggle="tab"
                                                                data-bs-target="#order-detail{{ isset($row->id) ? $row->id : '' }}"
                                                                type="button" role="tab"
                                                                aria-controls="order-detail{{ isset($row->id) ? $row->id : '' }}"
                                                                aria-selected="false">Influencer Tasks
                                                            </button>
                                                        </li>
                                                    </ul>
                                                    <div class="tab-content" id="myTabContent">
                                                        <div class="tab-pane fade show active"
                                                            id="general-information{{ isset($row->id) ? $row->id : '' }}"
                                                            role="tabpanel"
                                                            aria-labelledby="general-information-tab{{ isset($row->id) ? $row->id : '' }}">

                                                            <div class="inside-table request-content">
                                                                <div class="inside-table-row">
                                                                    <span class="type-label">Company Name</span>
                                                                    <span class="type-image"><img
                                                                            src="{{ asset('/') }}/assets/front-end/images/icons/icon-user-black.svg"
                                                                            class="" alt=""></span>
                                                                    <span
                                                                        class="type-content">{{ $row->user->company_name }}</span>
                                                                </div>
                                                                <div class="inside-table-row">
                                                                    <span class="type-label">Request date</span>
                                                                    <span class="type-image"><img
                                                                            src="{{ asset('/') }}/assets/front-end/images/icons/icon-calender-black.svg"
                                                                            class="" alt=""></span>
                                                                    <span
                                                                        class="type-content">{{ date('d.m.Y', strtotime($row->created_at)) }}</span>
                                                                </div>
                                                                <div class="inside-table-row">
                                                                    <span class="type-label">Total cost</span>
                                                                    <span class="type-image"><img
                                                                            src="{{ asset('/') }}/assets/front-end/images/icons/req-money.svg"
                                                                            class="" alt=""></span>
                                                                    <span
                                                                        class="type-content">{{ number_format($row->current_price, 2) }}
                                                                        €</span>
                                                                </div>
                                                                <div class="inside-table-row">
                                                                    <span class="type-label">Social Media</span>
                                                                    <span class="type-image"><img
                                                                            src="{{ asset('/') }}/assets/front-end/images/icons/icon-cb-{{ $row->media }}.svg"
                                                                            class="" alt=""></span>
                                                                    <span
                                                                        class="type-content">{{ ucfirst($row->media) }}</span>
                                                                </div>
                                                                <div class="inside-table-row">
                                                                    <span class="type-label">Brand name</span>
                                                                    <span class="type-image"><img
                                                                            src="{{ asset('/') }}/assets/front-end/images/icons/icon-brandname-black.svg"
                                                                            class="" alt=""></span>
                                                                    <span class="type-content">{{ $row->name }}</span>
                                                                </div>
                                                                <div class="inside-table-row">
                                                                    <span class="type-label">Campaign type</span>
                                                                    <span class="type-image"><img
                                                                            src="{{ asset('/') }}/assets/front-end/images/icons/icon-boostme-black.svg"
                                                                            class="" alt=""></span>
                                                                    <span
                                                                        class="type-content">{{ $row->post_type }}</span>

                                                                </div>
                                                                @if (isset($row->category))
                                                                    <div class="inside-table-row">
                                                                        <span class="type-label">Category</span>
                                                                        <span class="type-image"><img
                                                                                src="{{ asset('/') }}/assets/front-end/images/icons/icon-category-black.svg"
                                                                                class="" alt=""></span>
                                                                        <span
                                                                            class="type-content">{{ $row->category->name }}</span>
                                                                    </div>
                                                                @endif
                                                            </div>
                                                        </div>
                                                        <div class="tab-pane fade"
                                                            id="order-detail{{ isset($row->id) ? $row->id : '' }}"
                                                            role="tabpanel"
                                                            aria-labelledby="order-detail-tab{{ isset($row->id) ? $row->id : '' }}">
                                                            <div class="request-content-data icon-before">




                                                                @php $tasks = $row->tasks ; @endphp
                                                                @if (isset($tasks))
                                                                    @foreach ($tasks as $task)
                                                                        @if (isset($task->taskDetail) && $task->type == 'SocialMediaName')
                                                                            <div class="inside-table-row">
                                                                                <div class="order-titles">
                                                                                    {{ $task->taskDetail->task }}
                                                                                </div>
                                                                                <div class="order-content">
                                                                                    {{ $task->value }}
                                                                                </div>
                                                                            </div>
                                                                        @endif
                                                                    @endforeach


                                                                    @foreach ($tasks as $task)
                                                                        @if (isset($task->taskDetail) && $task->type == 'Link')
                                                                            <div class="inside-table-row">
                                                                                <div class="order-titles">
                                                                                    {{ $task->taskDetail->task }}
                                                                                </div>
                                                                                <div class="order-content">
                                                                                    <div class="order-link">
                                                                                        <div class="link"
                                                                                            id="myInput{{ $task->id }}">
                                                                                            {{ $task->value }}</div>
                                                                                        <div class="copy-link">
                                                                                            <a class="copy_text"
                                                                                                id="jjhu"
                                                                                                data-toggle="tooltip"
                                                                                                title="Copy to Clipboard"
                                                                                                href="{{ $task->value }}">
                                                                                                <span
                                                                                                    class="">COPY</span>
                                                                                            </a>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        @endif
                                                                    @endforeach


                                                                    @foreach ($tasks as $task)
                                                                        @if (isset($task->taskDetail) && $task->type == 'UploadContent')
                                                                            <div class="inside-table-row">
                                                                                <div class="order-titles">
                                                                                    {{ $task->taskDetail->task }}
                                                                                </div>
                                                                                <div class="order-content">


                                                                                    <a class="table-btn"
                                                                                        href="{{ asset('storage/' . $task->value) }}"
                                                                                        download
                                                                                        style="color: black !important;width:186px !important;height:40px;box-shadow:none !important;">
                                                                                        Download
                                                                                    </a>
                                                                                    @if ($row->post_content_type == 'video')
                                                                                        <img src="{{ url('/assets/front-end/icons/video_placeholder.png') }}"
                                                                                            width="40">
                                                                                    @else
                                                                                        <img src="{{ url('/assets/front-end/icons/image_placholder.png') }}"
                                                                                            width="40">
                                                                                    @endif

                                                                                </div>
                                                                            </div>
                                                                        @endif
                                                                    @endforeach

                                                                    @foreach ($tasks as $task)
                                                                        @if (isset($task->taskDetail) && $task->type == 'Hashtag')
                                                                            <div class="inside-table-row">
                                                                                <div class="order-titles">
                                                                                    {{ $task->taskDetail->task }}
                                                                                </div>
                                                                                <div class="order-content">
                                                                                    <?php $tags = explode(',', $task->value); ?>
                                                                                    @foreach ($tags as $tag)
                                                                                        @if ($tag)
                                                                                            <div class="order-hash-tag">
                                                                                                <img src="{{ asset('/') }}/assets/front-end/images/icon-hash.png"
                                                                                                    alt="">
                                                                                                {{ $tag }}
                                                                                            </div>
                                                                                        @endif
                                                                                    @endforeach

                                                                                </div>
                                                                            </div>
                                                                        @endif
                                                                    @endforeach

                                                                    @foreach ($tasks as $task)
                                                                        @if (isset($task->taskDetail) && $task->type == 'Info')
                                                                            <div class="inside-table-row">
                                                                                <div class="order-titles">
                                                                                    {{ $task->taskDetail->task }}
                                                                                </div>
                                                                            </div>
                                                                        @endif
                                                                    @endforeach
                                                                @endif


                                                            </div>

                                                        </div>
                                                    </div>


                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        @endforeach
                    @else
                        <div class="no-data-div">
                            <img src="{{ asset('/') }}/assets/front-end/images/icons/icon-no-data-image.png"
                                alt="">
                            <div class="no-data-contant">
                                Sorry, you have no campaing history at the moment.
                            </div>
                        </div>
                    @endif

                    <div class="modal fade complaint-confirm-popup influncer" id="complaintconfirm"
                        data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
                        aria-labelledby="reviewRatingPopupLabel" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-body">
                                    <a href="{{ url('campaign-history') }}"><button type="button" class="btn-close"
                                            data-bs-dismiss="modal" aria-label="Close"><img
                                                src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg"
                                                alt=""></button></a>

                                    <div class="complaint-confirm text-center">
                                        {{-- <img src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg" alt="" class="complaint-confirm-image"> --}}
                                        <div class="text-center chkImage"><img
                                                src="{{ asset('/') }}/assets/front-end/images/icons/icon-green-border-check.svg"
                                                alt=""></div>
                                        <p class="complaint-confirm-text">
                                            We notify the Influencer about the complaint and request a statement about it.
                                            After
                                            reviewing the matter, we will sent you the results of the complaint.
                                        </p>
                                        <p class="complaint-confirm-text">
                                            The review can take up to 7 days. We ask you for patience and please check your
                                            mails and
                                            messages regurlarly
                                        </p>
                                        <a href="{{ url('campaign-history') }}" class="et-submit mx-4 complant-btn"
                                            aria-label="Close">Confirm</a>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>


            </div>




    </section>
    <div class="loaderss" id="pageLoader">
        <img src="{{ asset('/') }}/assets/front-end/images/loading-loading-forever.gif" alt="">
    </div>
@endsection

@section('script_links')
    <script type="text/javascript" src="https://cdn.datatables.net/v/bs5/dt-1.13.1/datatables.min.js"></script>
    <script type="text/javascript" src="{{ URL::asset('/assets/front-end/js/loadMoreResults.js') }}"></script>
@endsection

@section('script_codes')
    <script>
        $(document).ready(function() {
            // $('#venueTable').DataTable();
            var complaint = @json($complaint);
            if (complaint == 1) {
                console.log(complaint)
                $('#complaintconfirm').modal('show');
            }
        });

        $(document).ready(function() {
            $('#accordionHistory').loadMoreResults({
                displayedItems: 5,
                showItems: 5,
                button: {
                    'class': 'btn-load-more',
                    'text': 'Load More'
                }
            });
        })

        function reviewTask(slug, id) {
            var review_count = 0;

            var task1 = $('input[name="task1"]:checked').val();
            var task2 = $('input[name="task2"]:checked').val();
            var task3 = $('input[name="task3"]:checked').val();

            console.log('task1' + task1);
            console.log('task2' + task2);
            console.log('task3' + task3);

            if (task1 != undefined) {
                var review_count = parseInt(review_count) + 1;
            }
            if (task2 != undefined) {
                var review_count = parseInt(review_count) + 1;
            }
            if (task3 != undefined) {
                var review_count = parseInt(review_count) + 1;
            }

            console.log('review_count' + review_count);
            var task = parseInt(task1) + parseInt(task2) + parseInt(task3);
            if (task == 3) {
                $('#review' + id).prop("disabled", false);
                $('#complaint' + id).hide();
                $('#complaint' + id).prop("disabled", true);
            } else {
                if (review_count == 3) {
                    $('#complaint' + id).show();
                    $('#complaint' + id).prop("disabled", false);
                    $('#review' + id).prop("disabled", true);
                }
            }

        }

        function requestSubmitbutton(id) {
            $("#pageLoader").show()
            $.ajax({
                url: "{{ URL::to('/request-review') }}/" + id,
                method: 'GET',
            }).done(function(data) {
                // console.log(data);
                $('.campHistory').html(data);
                $('#reviewRating' + id).modal('show');
                // window.location.reload();
                $("#pageLoader").hide()
            }).fail(function() {
                console.log('fail');
                $("#pageLoader").hide()
            });
        }



        // function raiseComplaint(id){
        //     var data = new FormData();
        //     // var data = $('#complaintForm'+id).serializeArray();
        //     console.log(data);
        //     var influencer_request_accept_id = $('#influencer_request_accept_id'+id).val();
        //     var comment = $('#comment').val();
        //     var file_data = $('input[name="complaint_file"]')[0].files[0];
        //     data.append('influencer_request_accept_id', influencer_request_accept_id);
        //     data.append('comment', comment);
        //     data.append('file', file_data);
        //     data.append('_token', '{{ csrf_token() }}')


        //     $("#pageLoader").show()
        //     $.ajax({
        //         url: "{{ URL::to('/submit-complaint') }}",
        //         method: 'POST',
        //         data: data,
        //         processData: false,
        //         contentType: false

        //     }).done(function (response) {

        //         $('#contact-review-popup'+id).modal('hide');
        //         $('#complaintconfirm'+id).modal('show');
        //             // window.location.reload();
        //         $("#pageLoader").hide()
        //     }).fail(function () {
        //         console.log('fail');
        //         $("#pageLoader").hide()
        //     });
        // }
    </script>
@endsection
