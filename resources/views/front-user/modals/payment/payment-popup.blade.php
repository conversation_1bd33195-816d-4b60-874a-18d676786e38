@php
    $campaignId = $row->compaign_id;
    $amount = 0;
    $lists = App\Models\InfluencerRequestDetail::where('compaign_id', $row->compaign_id)->where('status', null)->get();
@endphp
<!-- Show Detail-->
<style>
    @media screen and (max-width: 575px) {
        .modal-content {
            width: 90vw !important;
            min-height: 90vh !important;
            border-radius: 15px !important;
        }

        .modal-dialog.default-width {
            width: 90vw !important;
            max-width: unset;
            margin: 5vw;
        }

        .campaign-influencer {
            padding: 0 !important;
        }

        .summary-item {
            font-size: 13px !important;
        }

        .summary-item .value {
            font-size: 13px !important;
        }

        .payment-page {
            margin: 0 !important;
        }

        .expiry-month {
            padding-right: 5px;
        }

        .expiry-year {
            padding-left: 5px;
        }

        .desktop-view-card,
        .hide-for-mobile {
            display: none;
        }

        .payment-form {
            margin-top: 10px
        }
    }

    @media screen and (min-width: 575px) {
        .mobile-view-card {
            display: none;
        }
    }

    .campaign-influencer {
        padding: 0 0 0 30px;
    }

    .acknowledgment-check {
        font-weight: 400;
    }

    input::placeholder {
        color: #acb9c6;
        opacity: 1;
    }
</style>

<div class="modal fade influncer wewPopup"
    id="startCampaign{{ $row->compaign_id }}"
    data-bs-backdrop="static"
    data-bs-keyboard="false"
    tabindex="-1"
    aria-labelledby="requestFormLabel"
    aria-hidden="true">
    <div class="modal-dialog default-width modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    <img src="{{ asset('/assets/front-end/images/icons/close-one.svg') }}" alt="">
                </button>
                <form method="post" action="{{ url('/request-start-campaign') }}" id="paymentCmpaignForm" data-parsley-validate>
                    @csrf
                    <ul class="nav nav-tabs ordertab">
                        <li class="nav-item" role="presentation">
                            <span style="text-align: center;" class="nav-link campaignOverviewTab active" id="campaignOverviewTab">
                                Campaign Overview
                            </span>
                        </li>
                        <li class="nav-item" role="presentation">
                            <span style="text-align: center;" class="nav-link pointer-e-none" id="makePaymrntTab">
                                Payment
                            </span>
                        </li>
                    </ul>
                    <div class="tab-content" id="myTabContent">
                        {{-- *************************** --}}
                        {{-- Campaign Overview Tab Start --}}
                        {{-- *************************** --}}
                        <div class="tab-pane fade show active" id="general-information" role="tabpanel" aria-labelledby="campaignOverviewTab">
                            <div class="ontro">
                                <div class="droporg">
                                    {{ $row->compaign_title }} <br>
                                    <span id="campaignId">ID # {{ $row->compaign_id }}</span>
                                </div>
                                @php
                                    $amt = 0;
                                    $tot_follower = 0;
                                    $total_amount = 0;
                                    $total_VAT = 0;
                                @endphp
                                @foreach ($lists as $list)
                                    {{-- TODO A temporary fix for badly/incomplete deletion of influencer data from admin panel  --}}
                                    @if (empty($list->influencerdetails))
                                        @php continue; @endphp
                                    @endif
                                    @if (isset($list->influencer_request_accepts->request) && $list->influencer_request_accepts->request == 1)
                                        @php
                                            $socialName = App\Models\SocialConnect::where(
                                                'user_id',
                                                $list->influencerdetails->user->id,
                                            )
                                            ->where('media', $row->media)
                                            ->first();

                                            if (empty($socialName)) {
                                                throw new Exception('Something went wrong. Influencer has no social connection.');
                                            }

                                            $tot_follower = $tot_follower + empty($socialName->followers) ? 0 : $socialName->followers;
                                            $total_amount = $total_amount + $list->discount_price;

                                        @endphp
                                        <div class="campaign-list {{ $list->influencerdetails->user->id }}">
                                            <div class="campaign-item row">
                                                <div class="campaign-influencer col-5"
                                                    style="margin: 0; display:flex; align-items: center;">
                                                    <img src="{{ asset('storage/' . $socialName->picture) }}"
                                                        alt="Influencer" class="influencer-pic">
                                                    <div class="influencer-details">
                                                        <a href="{{ $socialName->url }}" class="influencer-name"
                                                            target="_blank" style="color: #212529; ">@
                                                            {{ $socialName->name }}</a>
                                                        <span class="follower-count-val"><span
                                                                id="follower-count-val">{{ $socialName->followers }}</span>
                                                            Followers</span>
                                                    </div>
                                                </div>
                                                <div class="campaign-pricing col-6"
                                                    style="margin: 0; padding:0; display:flex;">
                                                    @php
                                                        $newLivetreamPrice = 0;
                                                        $addNewLivetreamPrice = 0;
                                                        
                                                        $user = App\Models\User::find($list->influencerdetails->user->id);
                                                        
                                                        $fieldName = $list->advertising . '_price';
                                                        if ($user->advertisingMethodPrice != null) {
                                                            $newLivetreamPrice = $user->advertisingMethodPrice->$fieldName;
                                                        }
                                                        $amt = $amt + $newLivetreamPrice;
                                                        
                                                        $VAT_value = $user->is_small_business_owner ? 0 : $list->discount_price * 0.19;
                                                        
                                                        $total_VAT += $VAT_value;
                                                    @endphp
                                                    <div class="row w-100">
                                                        <span class="user_price-val col-4">
                                                            € <span id="user_price-val">{{ number_format($list->discount_price, 2) }}</span>
                                                        </span>
                                                        <span class="campaign_vat_price col-8" style="margin: 0; padding:0;">
                                                            @if ($user->is_small_business_owner)
                                                                <span id="vat_value">0</span> € <span style="font-size: 8px; font-weight:100;">(Small business owner according to § 19 UStG)</span>
                                                            @else
                                                                <span id="vat_value">{{ number_format($VAT_value, 2) }}</span>
                                                                € <span style="font-size: 8px; font-weight:100;">(VAT 19%)</span>
                                                            @endif
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="remove-icon col-1"
                                                    style="margin: 0; padding:0; text-align: center;">
                                                    <img src="{{ asset('/assets/front-end/images/new/delete.svg') }}" data-delete="{{ $list->influencerdetails->user->id }}">
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                @endforeach
                                @php
                                    $platform_fee = ($total_amount * 5) / 100;
                                    if ($platform_fee < 2) {
                                        $platform_fee = 2;
                                    }
                                    $total_VAT = $total_VAT + $platform_fee * 0.19;
                                    // TODO is this value being used?
                                    $comission =
                                        $row->discount_price == 1
                                            ? $AdminComission->customer - 5
                                            : $AdminComission->customer;

                                    // TODO Is this value is being used?
                                    $amount = $amount + ($amount * $comission) / 100;
                                @endphp
                                <div class="order-summary">
                                    <div class="summary-column" style="width:auto;">
                                        <div class="summary-item">
                                            <span class="label">Subtotal:</span>&nbsp;
                                            <span class="value">
                                                <span id="follower_final_new">{{ $tot_follower }}</span> Followers
                                            </span>
                                        </div>
                                    </div>
                                    <div class="summary-column"
                                        style="display: flex; flex-direction:column; width:auto;">
                                        <div class="summary-item">
                                            <span class="label">Price:</span>&nbsp;
                                            <span class="value">
                                                € <span id="subtotal_final_new">{{ number_format($total_amount, 2) }}</span> (VAT excluded)
                                            </span>
                                        </div>
                                        <div class="summary-item">
                                            <span class="label">Platform Fee (5%):</span>&nbsp;
                                            <span class="value">
                                                € <span id="fee_final_new">{{ number_format($platform_fee, 2) }}</span> (VAT excluded)
                                            </span>
                                        </div>
                                        <div class="summary-item">
                                            <span class="label">VAT:</span>&nbsp;
                                            <span class="value">
                                                € <span id="vat_final_new">{{ number_format($total_VAT, 2) }}</span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="total-container">
                                    <div class="total-item">
                                        <span class="total-label">Order Total:</span>
                                        <span class="total-value" style="text-align: end;">
                                            <div>€ {{ number_format($total_amount + $platform_fee + $total_VAT, 2) }}
                                            </div>
                                            <div style="font-size: 15px; font-weight: 200;">(VAT included)</div>
                                        </span>
                                    </div>
                                </div>
                                <div class="influncerlist-dropdown">
                                    <div class="accordion" id="accordionExample">
                                        <div class="text-center continue-link-outer nav nav-tabs ordertab"
                                            id="myTab" role="tablist">
                                            <a href="javascript:void(0)" id="makePaymentContinue"
                                                class="continue-link" type="button"
                                                style="background-color: #AD80FF; border: solid 1px #AD80FF; border-radius: 10px;"
                                                @if ($total_amount < 1) disabled @endif>Continue</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {{-- Campaign Overview Tab Ends --}}

                        {{-- ***************** --}}
                        {{-- Payment Tab Start --}}
                        {{-- ***************** --}}
                        <div class="tab-pane fade" id="makePaymrntDetail{{ $row->id }}" role="tabpanel" aria-labelledby="makePaymrntTab">
                            <div class="payment-page row m-5">
                                <div class="payment-total col-sm-6 px-0 d-flex flex-column">
                                    <div class="row">
                                        <div class="payment-title mt-0">Campaign Cost:</div>
                                    </div>
                                    <div class="payment w-100">
                                        <div class="d-flex">
                                            <span>Subtotal (VAT excl.): </span>
                                            <span class="ms-auto"> {{ number_format($total_amount, 2) }} €</span>
                                        </div>
                                        <div class="d-flex">
                                            <span>Platform Fee (VAT excl.): </span>
                                            <span class="ms-auto">{{ number_format($platform_fee, 2) }} €</span>
                                        </div>
                                        <div class="d-flex">
                                            <span>VAT: </span>
                                            <span class="ms-auto">{{ number_format($total_VAT, 2) }} €</span>
                                        </div>
                                        <div class="d-flex bold">
                                            <span>Order Total:</span>
                                            <span class="ms-auto">{{ number_format($total_amount + $platform_fee + $total_VAT, 2) }} €</span>
                                        </div>
                                    </div>
                                    <div id="error-message">
                                    </div>
                                    @if (isset($card))
                                        <div class="desktop-view-card card-design-outer mt-4" id="savedCard">
                                            <div class="card-design w-100"
                                                style="background-image: url({{ asset('assets/front-end/images/credit_card.png') }}); background-size: cover; height: 12rem">
                                                <div>
                                                    <img class="m-3 float-end" src="{{ asset('assets/front-end/images/visa-white.svg') }}">
                                                </div>
                                                <div class="card-nm mt-0 fs-4" style="margin-left: 20px;">
                                                    <span>****</span>
                                                    <span>****</span>
                                                    <span>****</span>
                                                    <span id="card-end-number">{{ $card->last4 }}</span>
                                                </div>
                                                <div class="card-iconsl" style="margin-left: 20px;">
                                                    <img src="{{ asset('assets/front-end/images/sim_icon.svg') }}">
                                                </div>
                                                <div class="crim d-flex justify-content-between"
                                                    style="margin: 20px; font-size:13px; color:white;">
                                                    <div>
                                                        <div>Card Holder name</div>
                                                        <div id="card-holder-name">{{ $card->name_on_card }}</div>
                                                    </div>
                                                    <div>
                                                        <div>Expiry Date</div>
                                                        <div id="card-expiry-date">{{ $card->expiry_month }}/{{ $card->expiry_year }}</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row m-3 d-flex justify-content-center">
                                                <div class="card-remove-btn m-0" id="remove_card" onclick="removeCard()">Remove</div>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                                <div class="payment-form col-sm-6">
                                    <div class="p-4" style="box-shadow: 0 5px 16px 12px rgba(0, 0, 0, 0.1); border-radius: 10px;" id="stripe_card">
                                        @if (isset($card))
                                            <div class="mobile-view-card card-design-outer mt-4" id="savedCardMobile">
                                                <div class="card-design w-100" style="background-image: url({{ asset('assets/front-end/images/credit_card.png') }}); background-size: cover; height: 12rem">
                                                    <div>
                                                        <img class="m-3 float-end" src="{{ asset('assets/front-end/images/visa-white.svg') }}">
                                                    </div>
                                                    <div class="card-nm mt-0 fs-4" style="margin-left: 20px;">
                                                        <span>****</span>
                                                        <span>****</span>
                                                        <span>****</span>
                                                        <span>{{ $card->last4 }}</span>
                                                    </div>
                                                    <div class="card-iconsl" style="margin-left: 20px;">
                                                        <img src="{{ asset('assets/front-end/images/sim_icon.svg') }}">
                                                    </div>
                                                    <div class="crim d-flex justify-content-between" style="margin: 20px; font-size:13px; color:white;">
                                                        <div>
                                                            <div>Card Holder name</div>
                                                            <div>{{ $card->name_on_card }}</div>
                                                        </div>
                                                        <div>
                                                            <div>Expiry Date</div>
                                                            <div>{{ $card->expiry_month }}/{{ $card->expiry_year }}</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row m-3 d-flex justify-content-center">
                                                    <div class="card-remove-btn m-0" onclick="removeCard()">Remove</div>
                                                </div>
                                            </div>
                                        @endif
                                        <div class="{{ isset($card) ? 'hide-for-mobile' : '' }}" id="card-form" style="width: 100%; {{ isset($card) ? 'opacity: 0.5' : '' }}">
                                            <div class="d-flex justify-content-center gap-4 my-2">
                                                <img src="{{ asset('/assets/front-end/images/fontisto_visa.svg') }}" alt="">
                                                <img src="{{ asset('/assets/front-end/images/logos_mastercard.svg') }}" alt="">
                                                <img src="{{ asset('/assets/front-end/images/fontisto_american-express.svg') }}" alt="">
                                            </div>
                                            <div class="row">&nbsp;</div>
                                            <div id="card-container" style="display: none;">
                                                <div class="row">
                                                    <div class="col-md-12 col-12">
                                                        <input type="text"
                                                            id="cardholder-name"
                                                            name="name_on_card"
                                                            class="form-control"
                                                            placeholder="Cardholder name"
                                                            style="border: 1px solid #ebebff; font-size: 16px; font-weight: normal; background-color: #F9F9FC; height: 3em;">
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-12 col-12">
                                                        <div id="card-element" style="margin-top: 20px;">
                                                            <!-- Stripe Elements will create the card input field here -->
                                                        </div>
                                                        <div id="card-errors" role="alert" style="color: red; margin-top: 10px;"></div>
                                                    </div>
                                                </div>
                                                <div class="row">&nbsp;</div>
                                                <div class="row full-field d-flex justify-content-center">
                                                    <div class="customcheckbox m-0" style="font-size: 16px; color: #acb9c6; font-weight: normal; height: 3em; width: 92%;">
                                                        <input type="checkbox" name="save_card" value="1" id="save_card" {{ isset($card) ? 'disabled' : '' }} class="ds">
                                                        <label class="customcheckboxlabel m-0" style="font-size: 16px; color: #acb9c6; font-weight: normal; padding-left: 20px; padding-bottom: 15px;">
                                                            <i class="fas fa-check" aria-hidden="true"></i> Save payment method
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <div class="d-flex my-3">
                                        <input type="checkbox" name="accept_platform_fees_nonrefundable"
                                            value="1" class="ds mx-2" required>
                                        <label class="acknowledgment-check m-0">
                                            I accept that the 5% platform fees are non-refundable regardless of the
                                            success or failure of the campaign(s).
                                        </label>
                                    </div>
                                    <div class="d-flex my-3">
                                        <input type="checkbox" name="accept_successful_condition" value="1"
                                            class="ds mx-2" required>
                                        <label class="acknowledgment-check m-0">
                                            The campaign will automatically be considered successful if it is not
                                            reviewed by me during the review phase of the campaign
                                        </label>
                                    </div>
                                </div>
                                <div class="text-center continue-link-outer mt-2 mb-0">
                                    <input type="hidden" name="compaign_id" id="payName" value="{{ $row->compaign_id }}">
                                    <input type="hidden" name="amount" id="payAmount" value="{{ $row->total_amount }}">
                                    <input type="hidden" name="name_on_card" value="{{ isset($card) ? $card->name_on_card : '' }}">
                                    <input type="hidden" name="payment_method_id" value="{{ isset($card) ? $card->payment_method : '' }}">
                                    <input type="hidden" name="card_brand" value="{{ isset($card) ? $card->type : '' }}">
                                    <input type="hidden" name="last4" value="{{ isset($card) ? $card->last4 : '' }}">
                                    <input type="hidden" name="expiry_month" value="{{ isset($card) ? $card->expiry_month : '' }}">
                                    <input type="hidden" name="expiry_year" value="{{ isset($card) ? $card->expiry_year : '' }}">
                                    <button type="submit"
                                        name="submitBtn"
                                        id="payAndStartFormBtn"
                                        class="finish-camp btn btn-finish-campaign mt-2 mb-0"
                                        style="border: none; width: 150px; border-radius: 10px;">Pay & Start</button>
                                </div>
                            </div>
                        </div>
                        {{-- Payment Tab Ends --}}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<!-- end -->
<div class="loaderss" id="pageLoader">
    <img src="{{ asset('/assets/front-end/images/loading-loading-forever.gif') }}" alt="">
</div>
<script type="text/javascript" src="{{ URL::asset('/assets/front-end/js/parsley.min.js') }}"></script>

<script>
    const paymentMethodId = '{{ isset($card) ? $card->payment_method : '' }}';
    const campaignId = '{{ $campaignId }}';
    const socialNetworkName = '{{ $row->media }}'; // Generally the name of the social network, eg, instagram, facebook, etc.
    const totalAmount = {{ $total_amount }};
    const platformFee = {{ $platform_fee}};
    const totalVAT = {{ $total_VAT }};

    console.log('Payment Method Id: ' + paymentMethodId);
    console.log('Campaign Id: ' + campaignId);
    console.log('Total amount: ' + totalAmount);
    console.log('Platform Fee: ' + platformFee);
    console.log('Total VAT: ' + totalVAT);
    console.log('Social Network Name: ' + socialNetworkName);

    // Initialize Stripe
    const env = '{{ config('app.env') }}';
    const liveKey = 'pk_live_51NXWAMFnPRFk0PucyHUV1wQZSFduO8oTbobXZeOh1EYG5DQ1Y4qmY2O36Xp5pPErxvQyFvawPXraEv3pFlfsP6ZI00hmRIfT0x';
    const testKey = 'pk_test_51QFwEpFv5DX420hy8bjA6TvFZzREN0asZrSqdDTbuxwhinzXLqC14NrgZS2WgW4iv8CdxZLgZfA8Fzs7BsmTsIeg00kpTpZv75';
    var stripe = Stripe(env === 'production' ? liveKey : testKey);
    var elements = stripe.elements();

    // Create an instance of the card Element
    var card = elements.create('card', {
        style: {
            base: {
                color: '#32325d',
                fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
                fontSmoothing: 'antialiased',
                fontSize: '16px',
                '::placeholder': {
                    color: '#aab7c4'
                }
            },
            invalid: {
                color: '#fa755a',
                iconColor: '#fa755a'
            }
        }
    });

    savedCard = @json($card ?? null);

    function enableNewCardForm() {
        $("[name='name_on_card']").prop('required', true);

        $('#card-container').show();

        // Add the card Element to the DOM
        card.mount('#card-element');

        // Handle real-time validation errors from the card Element
        card.on('change', function (event) {
            var displayError = $('#card-errors');
            if (event.error) {
                displayError.text(event.error.message);
            } else {
                displayError.text('');
            }
        });
    }

    function removeCard() {
        $('#savedCardMobile').addClass("d-none").removeClass("d-flex");
        $('#card-form').addClass("d-inline-block");

        $('#card-start-number').html('****');
        $('#card-end-number').html('****');
        $('#card-holder-name').html('');
        $('#card-expiry-date').html('');

        $('#card-form').css("opacity", '1');
        $('#cardholder_name').prop('disabled', false);
        $('#cardholder_number').prop('disabled', false);
        $('#card_month').prop('disabled', false);
        $('#card_year').prop('disabled', false);
        $('#card_cvv').prop('disabled', false);
        $('#save_card').prop('disabled', false);

        $("#remove_card").css({
            "opacity": '0.5',
            "cursor": "default",
            "pointer-events": "none"
        }).off("click");

        enableNewCardForm();
    }

    $(document).ready(function () {
        $(window).keydown(function(event) {
            if (event.keyCode == 13) {
                event.preventDefault();
                return false;
            }
        });

        $('#paymentCmpaignForm').parsley().on('form:validate', function(formInstance) {
            if (formInstance.isValid()) {
                $('#payAndStartFormBtn').prop('disabled', true);
                $("#pageLoader").show();
            }
        });

        if (savedCard == null) {
            enableNewCardForm(card);
        }

        $('#paymentCmpaignForm').on('submit', async function (event) {
            event.preventDefault();

            const formObject = this;
            const cardholderName = $('#cardholder-name').val();
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

            // Disable button and show loading
            $('#payAndStartFormBtn').prop('disabled', true);
            $("#pageLoader").show();

            try {
                // If no saved card, create a new payment method
                let paymentMethodIdToUse = paymentMethodId;

                if (!savedCard || paymentMethodId === '') {
                    const result = await stripe.createPaymentMethod({
                        type: 'card',
                        card: card,
                        billing_details: {
                            name: cardholderName,
                        },
                    });

                    if (result.error) {
                        throw new Error(result.error.message);
                    }

                    const paymentMethod = result.paymentMethod;
                    paymentMethodIdToUse = paymentMethod.id;

                    // Optionally save payment method details in hidden fields
                    $('input[name="payment_method_id"]').val(paymentMethodIdToUse);
                    $('input[name="name_on_card"]').val(paymentMethod.billing_details.name);
                    $('input[name="card_brand"]').val(paymentMethod.card.brand);
                    $('input[name="last4"]').val(paymentMethod.card.last4);
                    $('input[name="expiry_month"]').val(paymentMethod.card.exp_month);
                    $('input[name="expiry_year"]').val(paymentMethod.card.exp_year);

                    var cardDataToSave = {};
                    cardDataToSave.payment_method = paymentMethodIdToUse;
                    cardDataToSave.name_on_card = paymentMethod.billing_details.name;
                    cardDataToSave.card_brand = paymentMethod.card.brand;
                    cardDataToSave.last4 = paymentMethod.card.last4;
                    cardDataToSave.expiry_month = paymentMethod.card.exp_month;
                    cardDataToSave.expiry_year = paymentMethod.card.exp_year;

                    const intentRes = await fetch('/stripe/save-card', {
                        method: 'POST',
                        body: JSON.stringify(cardDataToSave),
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': csrfToken
                        }
                    });
                }

                // Create PaymentIntent from backend
                const payload = {
                    campaignId,
                    totalAmount,
                    platformFee,
                    totalVAT,
                    socialNetworkName,
                    paymentMethodIdToUse
                };

                const intentRes = await fetch('/stripe/create-payment-intent', {
                    method: 'POST',
                    body: JSON.stringify(payload),
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken
                    }
                });

                const { paymentIntentId, clientSecret, orderId } = await intentRes.json();

                if (!clientSecret) {
                    throw new Error('No client secret returned from server.');
                }

                // Confirm the payment (handles 3D Secure too)
                const confirmation = await stripe.confirmCardPayment(clientSecret, {
                    payment_method: paymentMethodIdToUse,
                    setup_future_usage: 'off_session'
                });

                if (confirmation.error) {
                    throw new Error(confirmation.error.message);
                }

                const intent = confirmation.paymentIntent;

                switch (intent.status) {
                    case 'succeeded':
                        console.log('✅ Payment succeeded!');

                        const retrievedIntent = await fetch('/stripe/retrieve-payment-intent', {
                            method: 'POST',
                            body: JSON.stringify({
                                'paymentIntentId': paymentIntentId
                            }),
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': csrfToken
                            }
                        });

                        const retrieveIntentRes = await retrievedIntent.json();
                        console.log(retrieveIntentRes);
                        if (!retrieveIntentRes.success) {
                            throw new Error(retrieveIntentRes.error);
                        }
                        const retrievedIntentChargeId = retrieveIntentRes.chargeId;

                        console.log('Retrieved intent charge Id ***********');
                        console.log(retrievedIntentChargeId);

                        // Start the campaign
                        const successRes = await fetch('/stripe/process-transfer-start-campaign', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': csrfToken
                            },
                            body: JSON.stringify({
                                payment_intent_id: intent.id,
                                order_id: orderId,
                                payment_method_id: paymentMethodIdToUse,
                                campaign_id: campaignId,
                                charge_id: retrievedIntentChargeId
                            })
                        });

                        const { campaignStarted, errorMsg } = await successRes.json();

                        if (!campaignStarted) {
                            throw new Error(errorMsg);
                        }

                        window.location.href = '/active-campaigns#' + campaignId;
                        break;

                    case 'requires_action':
                    case 'requires_source_action':
                        // Should not happen with confirmCardPayment, but include just in case
                        throw new Error('Additional authentication is required. Please try again.');

                    case 'requires_payment_method':
                    case 'canceled':
                    case 'processing':
                    case 'requires_confirmation':
                        throw new Error('Payment could not be completed. Please try another card or contact support.');

                    default:
                        throw new Error(`Failed payment status: ${intent.status}`);
                }
            } catch (error) {
                console.error(error);
                $('#card-errors').text(error.message);
            } finally {
                // Re-enable button and hide loader
                // Wait for 30 seconds before proceeding
                await new Promise(resolve => setTimeout(resolve, 15000));
                $("#pageLoader").hide();
                $('#payAndStartFormBtn').prop('disabled', false);
            }
        });
    });
</script>

