<!-- Modal Review Rating Popup -->
@if ($influencerRequestDetail->review == 1 && isset($influencerRequestDetail->influencer_request_accepts->rating_reviews))
<div class="modal fade influncer ratingPopup"
    id="reviewRatingPopup{{ $influencerRequestDetail->influencer_request_accepts->rating_reviews->id }}"
    data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
    aria-labelledby="reviewRatingPopupLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-body">
                <button type="button" class="btn-close" data-bs-dismiss="modal"
                    aria-label="Close"><img
                        src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg"
                        alt=""></button>
                <div class="wizardHeading">Review Details</div>
                <div class="wizardForm mt-4">
                    @csrf
                    <div class="col-xl-12 col-md-12 col-12">
                        <div class="form-group d-inline-block w-100">
                            <label>Overall rating</label>
                            <div class="rate infl">
                                <?php
                                $count = 5 - $influencerRequestDetail->influencer_request_accepts->rating_reviews->rating;
                                ?>
                                @for ($i = 0; $i < $influencerRequestDetail->influencer_request_accepts->rating_reviews->rating; $i++)
                                    <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png"
                                        alt="">
                                @endfor
                                @for ($j = 0; $j < $count; $j++)
                                    <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png"
                                        alt="">
                                @endfor
                            </div>
                        </div>
                        <div class="col-xl-12 col-md-12 col-12">
                            <div class="form-group">
                                <textarea id="review" placeholder="Type your review here..." name="review" readonly>{{ $influencerRequestDetail->influencer_request_accepts->rating_reviews->review }}</textarea>
                            </div>
                        </div>
                    </div>
                    <div class="text-center button-text">
                        <p>Do you want to dispute this review?</p>
                        @if (@$influencerRequestDetail->influencer_request_accepts->rating_reviews->is_dispute == 0)
                            <a href="#" class="light-red-btn support-btn"
                                data-bs-target="#contact-support{{ $influencerRequestDetail->id }}"
                                data-bs-toggle="modal" data-bs-dismiss="modal">Contact
                                support</a>
                        @else
                            <a href="#" style="pointer-events:none">You have
                                contacted support for dispute this review</a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endif

<!-- Modal Contact Support -->
<div class="modal fade confirm-content influncer" id="contact-support{{ $influencerRequestDetail->id }}"
    data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
    aria-labelledby="requestSubmit{{ $influencerRequestDetail->id }}Label" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-body">
                <button type="button" class="btn-close" data-bs-dismiss="modal"
                    aria-label="Close"><img
                        src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg"
                        alt=""></button>
                <div class="wizardHeading">Contact support</div>
                <div class="contact-support">
                    <div class="text-center wizardHeading-subheading">Please explain why
                        this rating is not appropriate</div>
                    <form action="{{ url('/review-dispute') }}" method="Post"
                        enctype="multipart/form-data" data-parsley-validate>
                        @csrf
                        <input type="hidden" name="review_id"
                            @if (isset($influencerRequestDetail->influencer_request_accepts->rating_reviews)) value="{{ $influencerRequestDetail->influencer_request_accepts->rating_reviews->id }}" @endif>
                        <input type="hidden" name="page" value="history">
                        <div class="form-group">
                            <label for="" class="form-label"></label>
                            <textarea class="form-control" name="comment" id="comment" rows="3"
                                placeholder="Please describe your problem" required data-parsley-required-message="Please enter problem."></textarea>
                        </div>
                        <div class="form-group uploadFile">
                            <div class="custom-file-picker">
                                <div class="picture-container form-group">
                                    <div class="picture">
                                        <span class="icon" id="icon">
                                            <div class="smaltext">Browse</div>
                                            <div class="bigtext">Or Drag and Drop to Upload
                                            </div>
                                        </span>
                                        <input type="file" class="wizard-file"
                                            id="supportFiles" name="file">
                                        <svg version="1.1"
                                            xmlns="http://www.w3.org/2000/svg"
                                            xmlns:xlink="http://www.w3.org/1999/xlink"
                                            x="0px" y="0px" viewBox="0 0 37 37"
                                            xml:space="preserve">
                                            <path class="circ path"
                                                style="fill:none;stroke:#77d27b;stroke-width:3;stroke-linejoin:round;stroke-miterlimit:10;"
                                                d="M30.5,6.5L30.5,6.5c6.6,6.6,6.6,17.4,0,24l0,0c-6.6,6.6-17.4,6.6-24,0l0,0c-6.6-6.6-6.6-17.4,0-24l0,0C13.1-0.2,23.9-0.2,30.5,6.5z">
                                            </path>
                                            <polyline class="tick path"
                                                style="fill:none;stroke:#77d27b;stroke-width:3;stroke-linejoin:round;stroke-miterlimit:10;"
                                                points="11.6,20 15.9,24.2 26.4,13.8 ">
                                            </polyline>
                                        </svg>
                                        <div
                                            class="popover-container text-center show-file">
                                            <p data-toggle="popover"
                                                data-id="a8755cf0-f4d1-6376-ee21-a6defd1e7c08"
                                                class="btn-popover" data-original-title=""
                                                title="">
                                                <span class="file-total-viewer">0</span>
                                                Files Selected <br /><input type="button"
                                                    value="view"
                                                    href="javascript:void(0)"
                                                    class="btn btn-success btn-xs btn-file-view">
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="extra-time-content-bs">
                            <input type="submit" class="et-submit ds" name="confirm" value="Submit">
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Show Detail Modal -->
<div class="modal fade influncer wewPopup request-popup"
    id="requestForm{{ $influencerRequestDetail->id }}" data-bs-backdrop="static"
    data-bs-keyboard="false" tabindex="-1" aria-labelledby="requestFormLabel"
    aria-hidden="true">
    <div class="modal-dialog default-width modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body">
                <button type="button" class="btn-close" data-bs-dismiss="modal"
                    aria-label="Close">
                    <img src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg"
                        alt="">
                </button>
                <div class="popup-title">
                    {{ $influencerRequestDetail->compaign_title }}
                </div>
                <div class="popup-title-id">
                    Campaign ID: {{ $influencerRequestDetail->compaign_id }}
                </div>
                <form method="post" id="requestFormSubmit{{ $influencerRequestDetail->id }}"
                    action="{{ url('/request-form') }}" data-parsley-validate>
                    @csrf
                    <input type="hidden" name="influencer_request_detail_id"
                        id="influencer_request_detail_id"
                        value="{{ isset($influencerRequestDetail->id) ? $influencerRequestDetail->id : '' }}">
                    <ul class="nav nav-tabs ordertab" id="myTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="general-information-tab"
                                data-bs-toggle="tab" data-bs-target="#general-information"
                                type="button" role="tab"
                                aria-controls="general-information"
                                aria-selected="true">General Information
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link"
                                id="order-detail-tab{{ $influencerRequestDetail->compaign_id }}"
                                data-bs-toggle="tab"
                                data-bs-target="#order-detail{{ $influencerRequestDetail->compaign_id }}"
                                type="button" role="tab"
                                aria-controls="order-detail" aria-selected="false">My
                                Tasks
                            </button>
                        </li>
                    </ul>
                    <div class="tab-content" id="myTabContent">
                        <div class="tab-pane fade show active" id="general-information"
                            role="tabpanel" aria-labelledby="general-information-tab">
                            <div class="inside-table request-content">
                                <div class="inside-table-row">
                                    <span class="type-label">Company Name</span>
                                    <span class="type-image"><img
                                            src="{{ asset('/') }}/assets/front-end/images/icons/icon-user-black.svg"
                                            class="" alt=""></span>
                                    @if (isset($influencerRequestDetail->user->company_name))
                                        <span
                                            class="type-content">{{ $influencerRequestDetail->user->company_name }}</span>
                                    @endif
                                </div>
                                <div class="inside-table-row">
                                    <span class="type-label">Request date</span>
                                    <span class="type-image"><img
                                            src="{{ asset('/') }}/assets/front-end/images/icons/icon-calender-black.svg"
                                            class="" alt=""></span>
                                    <span
                                        class="type-content">{{ date('d.m.Y', strtotime($influencerRequestDetail->created_at)) }}</span>
                                </div>
                                <div class="inside-table-row">
                                    <span class="type-label">You get</span>
                                    <span class="type-image"><img
                                            src="{{ asset('/') }}/assets/front-end/images/icons/req-money.svg"
                                            class="" alt=""></span>
                                    <span
                                        class="type-content">{{ number_format($influencerRequestDetail->current_price, 2) }}
                                        €</span>
                                </div>
                                <div class="inside-table-row">
                                    <span class="type-label">Social Media</span>
                                    <span class="type-image"><img
                                            src="{{ asset('/') }}/assets/front-end/images/icons/icon-cb-{{ $influencerRequestDetail->media }}.svg"
                                            class="" alt=""></span>
                                    <span
                                        class="type-content">{{ ucfirst($influencerRequestDetail->media) }}</span>
                                </div>
                                <div class="inside-table-row">
                                    <span class="type-label">Brand name</span>
                                    <span class="type-image"><img
                                            src="{{ asset('/') }}/assets/front-end/images/icons/icon-brandname-black.svg"
                                            class="" alt=""></span>
                                    <span class="type-content">{{ $influencerRequestDetail->name }}</span>
                                </div>
                                <div class="inside-table-row">
                                    <span class="type-label">Campaign type</span>
                                    <span class="type-image"><img
                                            src="{{ asset('/') }}/assets/front-end/images/icons/icon-boostme-black.svg"
                                            class="" alt=""></span>
                                    <span
                                        class="type-content">{{ $influencerRequestDetail->post_type }}</span>
                                </div>
                                @if (isset($influencerRequestDetail->category))
                                    <div class="inside-table-row">
                                        <span class="type-label">Category</span>
                                        <span class="type-image"><img
                                                src="{{ asset('/') }}/assets/front-end/images/icons/icon-category-black.svg"
                                                class="" alt=""></span>
                                        <span
                                            class="type-content">{{ $influencerRequestDetail->category->name }}</span>
                                    </div>
                                @endif
                            </div>
                        </div>
                        <div class="tab-pane fade"
                            id="order-detail{{ $influencerRequestDetail->compaign_id }}" role="tabpanel"
                            aria-labelledby="order-detail-tab{{ $influencerRequestDetail->compaign_id }}">
                            <div class="request-content-data icon-before">
                                @php $tasks = $influencerRequestDetail->tasks ; @endphp
                                @if (isset($tasks))
                                    @foreach ($tasks as $task)
                                        @if (isset($task->taskDetail) && $task->type == 'SocialMediaName')
                                            <div class="inside-table-row">
                                                <div class="order-titles">
                                                    {{ $task->taskDetail->task }}
                                                </div>
                                                <div class="order-content">
                                                    {{ $task->value }}
                                                </div>
                                            </div>
                                        @endif
                                    @endforeach
                                    @foreach ($tasks as $task)
                                        @if (isset($task->taskDetail) && $task->type == 'Link')
                                            <div class="inside-table-row">
                                                <div class="order-titles">
                                                    {{ $task->taskDetail->task }}
                                                </div>
                                                <div class="order-content">
                                                    <div class="order-link">
                                                        <div class="link"
                                                            id="myInput{{ $task->id }}">
                                                            {{ $task->value }}</div>
                                                        <div class="copy-link">
                                                            <a class="copy_text"
                                                                id="jjhu"
                                                                data-toggle="tooltip"
                                                                title="Copy to Clipboard"
                                                                href="{{ $task->value }}">
                                                                <span
                                                                    class="">COPY</span>
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        @endif
                                    @endforeach
                                    @foreach ($tasks as $task)
                                        @if (isset($task->taskDetail) && $task->type == 'UploadContent')
                                            <div class="inside-table-row">
                                                <div class="order-titles">
                                                    {{ $task->taskDetail->task }}
                                                </div>
                                                <div class="order-content">
                                                    <a class="table-btn"
                                                        href="{{ asset('storage/' . $task->value) }}"
                                                        download
                                                        style="color: black !important;width:186px !important;height:40px;box-shadow:none !important;">
                                                        Download
                                                    </a>
                                                    @if ($influencerRequestDetail->post_content_type == 'video')
                                                        <img src="{{ url('/assets/front-end/icons/video_placeholder.png') }}"
                                                            width="40">
                                                    @else
                                                        <img src="{{ url('/assets/front-end/icons/image_placholder.png') }}"
                                                            width="40">
                                                    @endif
                                                </div>
                                            </div>
                                        @endif
                                    @endforeach
                                    @foreach ($tasks as $task)
                                        @if (isset($task->taskDetail) && $task->type == 'Hashtag')
                                            <div class="inside-table-row">
                                                <div class="order-titles">
                                                    {{ $task->taskDetail->task }}
                                                </div>
                                                <div class="order-content">
                                                    <?php $tags = explode(',', $task->value); ?>
                                                    @foreach ($tags as $tag)
                                                        @if ($tag)
                                                            <div class="order-hash-tag">
                                                                <img src="{{ asset('/') }}/assets/front-end/images/icon-hash.png"
                                                                    alt="">
                                                                {{ $tag }}
                                                            </div>
                                                        @endif
                                                    @endforeach
                                                </div>
                                            </div>
                                        @endif
                                    @endforeach
                                    @foreach ($tasks as $task)
                                        @if (isset($task->taskDetail) && $task->type == 'Info')
                                            <div class="inside-table-row">
                                                <div class="order-titles">
                                                    {{ $task->taskDetail->task }}
                                                </div>
                                            </div>
                                        @endif
                                    @endforeach
                                @endif
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

{{-- Unnamed modal, perhaps a modal to show success message after contacting customer support --}}
<div class="modal fade complaint-confirm-popup influncer"
    id="thankYouContact"
    data-bs-backdrop="static"
    data-bs-keyboard="false"
    tabindex="-1"
    aria-labelledby="reviewRatingPopupLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-body">
                <a href="{{ url('/campaign-history') }}"> <button type="button" class="btn-close"
                        data-bs-dismiss="modal" aria-label="Close"><img
                            src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg"
                            alt=""></button></a>
                <div class="complaint-confirm text-center">
                    <img src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg" alt=""
                        class="complaint-confirm-image">
                    <p class="thank-title1">
                        Thank you for contacting us!
                    </p>
                    <p class="thank-title2">
                        We will be in touch with you soon
                    </p>
                    <a href="{{ url('/campaign-history') }}" class="et-submit mx-4 complant-btn"
                        aria-label="Close">Confirm</a>
                </div>
            </div>
        </div>
    </div>
</div>
