<!--modal taskModal form -->
<div class="modal fade influncer wewPopup" id="taskModal{{ $influencerCampaignDetail->campaign_id }}"
    data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
    aria-labelledby="taskModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body pb-5">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    <img src="{{ asset('/assets/front-end/images/icons/close-one.svg') }}" alt="">
                </button>
                <div class="wizardHeading">My Tasks</div>

                @php $tasks = $influencerCampaignDetail->tasks ; @endphp
                @if (isset($tasks))
                    @foreach ($tasks as $task)
                        @if (isset($task->taskDetail) && $task->type == 'SocialMediaName')
                            <div class="order-titles">
                                {{ $task->taskDetail->task }}
                            </div>
                            <div class="order-content">
                                {{ $task->value }}
                            </div>
                        @endif
                    @endforeach

                    @foreach ($tasks as $task)
                        @if (isset($task->taskDetail) && $task->type == 'Link')
                            <div class="order-titles">
                                {{ $task->taskDetail->task }}
                            </div>
                            <div class="order-content">
                                <div class="order-link">
                                    <div class="link"
                                        id="myInput{{ $task->id }}">
                                        {{ $task->value }}</div>
                                    <div class="copy-link">
                                        <a class="copy_text" id="jjhu"
                                            data-toggle="tooltip"
                                            title="Copy to Clipboard"
                                            href="{{ $task->value }}">
                                            <span class="">COPY</span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        @endif
                    @endforeach


                    @foreach ($tasks as $task)
                        @if (isset($task->taskDetail) && $task->type == 'UploadContent')
                            <div class="order-titles">
                                {{ $task->taskDetail->task }}
                            </div>
                            <div class="order-content">

                                @if ($influencerCampaignDetail->post_content_type == 'video')
                                    <video controls>
                                        <source
                                            src="{{ asset('storage/' . $task->value) }}"
                                            type="video/mp4">
                                    </video>
                                @else
                                    <a href="{{ asset('storage/' . $task->value) }}"
                                        download>
                                        <img src="{{ asset('storage/' . $task->value) }}"
                                            width="40">
                                    </a>
                                @endif

                                <!-- <a href="{{ asset('storage/' . $task->value) }}" download>
                                        <img src="{{ asset('storage/' . $task->value) }}" width="40">
                                     </a> -->
                            </div>
                        @endif
                    @endforeach

                    @foreach ($tasks as $task)
                        @if (isset($task->taskDetail) && $task->type == 'Hashtag')
                            <div class="order-titles">
                                {{ $task->taskDetail->task }}
                            </div>
                            <div class="order-content">
                                <?php $tags = explode(',', $task->value); ?>
                                @foreach ($tags as $tag)
                                    @if ($tag)
                                        <div class="order-hash-tag">
                                            <img src="{{ asset('/') }}/assets/front-end/images/icon-hash.png"
                                                alt="">
                                            {{ $tag }}
                                        </div>
                                    @endif
                                @endforeach

                            </div>
                        @endif
                    @endforeach

                    @foreach ($tasks as $task)
                        @if (isset($task->taskDetail) && $task->type == 'Info')
                            <div class="order-titles">
                                {{ $task->taskDetail->task }}
                            </div>
                        @endif
                    @endforeach
                @endif

                <div class="wizardForm">
                </div>
            </div>
        </div>
    </div>
</div>
<!--end modal taskModal form -->

<!--New popup start-->
<div class="modal fade" id="manyrequest" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
aria-labelledby="manyrequestLabel" aria-hidden="true">
<div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content">
        <div class="modal-body">

            <div class="text-center errorBox">
                <div class="text-center chkImage"><img
                        src="{{ asset('/') }}/assets/front-end/images/icons/icon-high-demand.svg"
                        alt=""></div>
                <div class="manreq-title">You are in high demand!</div>
                <div class="manreq-text">You have reached the maximum limit of 5 for your campaign/request total.
                    Please address your pending requests and complete your campaigns, so you can get requested
                    again.</div>
                <button class="btnpor bg-error color-white mb-5" type="button" data-bs-dismiss="modal">Let’s
                    go</button>
            </div>
        </div>
    </div>
</div>
</div>
<!--New popup end-->