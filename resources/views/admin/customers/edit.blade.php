@extends('admin.layouts.master_admin')

@section('page_last_name')
{{config('app.name')}} | Edit Brand
@endsection

@section('content')

    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="form-group">
                    <h1>Edit Brand</h1>
                </div>
            </div>
        </div><!-- /.container-fluid -->
    </section>
    
    <!--Begin Content-->
    <section class="content">

        <!-- Default box -->
        <div class="card">
            <div class="card-header">

                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse" data-toggle="tooltip"
                            title="Collapse">
                        <i class="fas fa-minus"></i></button>
                    
                </div>
            </div>
            <div class="card-body">
                <form action="" method="post"
                        id="add_business_term_form" enctype="multipart/form-data" data-parsley-validate>
                    @csrf
                    <fieldset> 
                        <div class="form-group">
                            <label for="exampleInputEmail4">First Name</label>
                            <input type="text" class="form-control" name="first_name"
                                    id="first_name" placeholder="Enter first name"  required=""data-parsley-required-message="Please enter first name." value="{{@$row->first_name}}">
                            @if ($errors->has('first_name'))
                                <span class="invalid-feedback" role="alert">
                                <strong>{{ $errors->first('first_name') }}</strong>
                                </span>
                            @endif
                        </div>
                        
                        <div class="form-group">
                            <label for="exampleInputEmail4">Last Name</label>
                            <input type="text" class="form-control" name="last_name"
                                    id="last_name" placeholder="Enter last name"  required=""data-parsley-required-message="Please enter last name." value="{{@$row->last_name}}">
                            @if ($errors->has('last_name'))
                                <span class="invalid-feedback" role="alert">
                                <strong>{{ $errors->first('last_name') }}</strong>
                                </span>
                            @endif
                        </div>

                        <div class="form-group">
                            <label for="exampleInputEmail4">Email</label>
                            <input type="email" class="form-control" name="email"
                                    id="email" placeholder="Enter email" required="" data-parsley-required-message="Please enter email." data-parsley-type-message="Please enter a valid email." value="{{@$row->email}}">
                            @if ($errors->has('email'))
                                <span class="invalid-feedback" role="alert">
                                <strong>{{ $errors->first('email') }}</strong>
                                </span>
                            @endif
                        </div>

                        <div class="form-group">
                            <label for="exampleInputEmail4">Phone</label>
                            <input type="text" class="form-control" name="phone"
                                    id="phone" placeholder="Enter phone" required="" data-parsley-required-message="Please enter phone." data-parsley-pattern="^[\d\+\-\.\(\)\/\s]*$" data-parsley-pattern-message="Please enter a valid phone number." data-parsley-maxlength="20" data-parsley-maxlength-message="Max length 20 numbers." value="{{@$row->phone}}">
                            @if ($errors->has('phone'))
                                <span class="invalid-feedback" role="alert">
                                <strong>{{ $errors->first('phone') }}</strong>
                                </span>
                            @endif
                        </div>
 
                          <div class="form-group">
                            <label>Brand Name</label>
                            <input type="text" placeholder="Brand name" name="company_name"   value="{{@$row->company_name}}"class="form-control" >
                          </div> 

                          <div class="form-group">
                            <label>Street</label>
                            <input type="text" placeholder="Street" name="street" required="" data-parsley-required-message="Please enter street." value="{{@$row->street}}"class="form-control" >
                          </div>
                          
                          <div class="form-group">
                            <label>Post/Zip Code</label>
                            <input type="number" placeholder="Post/Zip Code" name="zip_code" required="" data-parsley-required-message="Please enter Post/Zip Code." value="{{@$row->zip_code}}"class="form-control" >
                          </div>

                          <div class="form-group">
                            <label>Country</label>
                            <select placeholder="Country" name="country" required="" data-parsley-required-message="Please enter Country."  id="selectCountry" class="form-control"  >
                              <option value="">Country</option>
                              @foreach($countries as $country)
                                  <option @if(@$row->country == $country->id) Selected @endif   value="{{$country->id}}">{{$country->name}}</option>
                              @endforeach 
                            </select>
                          </div>
                          <div class="form-group">
                                <label>City</label>
                                <select  class="select form-control floating-input" name="city" required="" data-parsley-required-message="Please enter City."  id="selectCity"  >
                              <option value="">Select City</option>
                              @foreach($cities as $city)
                            @if($city->name != '')
                               @if(@$row->city == $city->id) 
                              <option Selected  value="{{$city->id}}">{{$city->name}}</option>
                               @endif 
                               @endif 
                              @endforeach 
                            </select>

                          </div>
                          <div class="form-group">
                            <label class="floatLabel">State/Province/Territory</label>
                            <input type="text" class="form-control floating-input" placeholder="&nbsp;" name="state"   data-parsley-required-message="Please enter state." value="{{@$row->state}}"> 
                          </div>

                        <div class="form-group">
                            <label for="exampleInputEmail4">Profile Pic</label>
                            <br />
                            @if(@$row->profile_pic!='')<img src="{{asset('storage/'.@$row->profile_pic)}}" height="100" width="100">@endif
                            <input type="hidden" name="oldImageValue" value="{{ @$row->profile_pic}}">
                            <br />
                            <label for="exampleInputEmail4">Update Profile Pic</label>
                            <input type="file" class="form-control img-upload-tag" name="profile_pic"
                                    id="profile_pic" data-parsley-required-message="Please upload main photo.">
                            @if ($errors->has('profile_pic'))
                                <span class="invalid-feedback" role="alert">
                                <strong>{{ $errors->first('profile_pic') }}</strong>
                                </span>
                            @endif
                        </div>

                        <input type="submit" class="btn btn-danger" value="Save">

                    </fieldset>
                </form>
            </div>
            <!-- /.card-body -->
            <div class="card-footer">
                {{--Footer--}}
            </div>
            <!-- /.card-footer-->
        </div>
        <!-- /.card -->

    </section>
    <!-- /.content -->

@endsection

@section('admin_script_codes')

 

<script>

     $('#selectCountry').change(function(){
        $("#selectCity").empty();
        $.get("{{url('admin/fetch-states')}}", {country: $(this).val()}, function(res){
            // /console.log(res);
            $("#selectCity").append(res);
            $("#selectCity").selectpicker('refresh');
        })
    });
 
</script>
@endsection
    
