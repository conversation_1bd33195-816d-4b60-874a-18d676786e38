<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('social_posts', function (Blueprint $table) {
            // Add composite unique constraint on user_id, post_id and media
            $table->unique(['user_id', 'post_id', 'media'], 'social_posts_user_post_media_unique');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('social_posts', function (Blueprint $table) {
            // Drop the unique constraint
            $table->dropUnique('social_posts_user_post_media_unique');
        });
    }
};
