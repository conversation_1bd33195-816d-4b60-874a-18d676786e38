<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\InfluencerRequestDetail;
use App\Models\SocialConnect;
use App\Models\SocialPost;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Exception;
use App\Models\User;
use App\Models\AdminGamification;
use App\Models\Statistic;
use App\Models\SocialKeys;
use App\Jobs\NewConfirmPostInfluencer;
use App\Jobs\NewtheInfluencersHaveDoneTheirJob;
use App\Notifications\ConfirmPostInfluencer;
use GuzzleHttp\Client;
use Storage;
use App\Helpers\Instagram\InsightsForTypePost;
use App\Helpers\Instagram\InsightsForTypeStory;

class InfluencerSubmissionController extends Controller
{
    /**
     * This method handles all influencer submission from the front-end
     *
     * Views rendered for this method:
     * @see resources/views/components/partials/influencer/submission/initiate.blade.php
     * @see resources/views/components/partials/influencer/submission/socialpost/post-type-survey.blade.php
     * @see resources/views/components/partials/influencer/submission/socialpost/post-type-reaction-video.blade.php
     * @see resources/views/components/partials/influencer/submission/socialpost/post-type-boost-me.blade.php
     * @param mixed $id
     * @return void
     */
    public function initiateInfluencerCampaignSubmission($influencerRequestDetailId)
    {
        try {
            $influencerRequestDetail = InfluencerRequestDetail::where('id', $influencerRequestDetailId)->first();
            if (!$influencerRequestDetail) {
                \Log::error('initiateInfluencerCampaignSubmission: InfluencerRequestDetail not found', [
                    'id' => $influencerRequestDetailId,
                    'auth_id' => Auth::id(),
                ]);
                throw new \Exception('InfluencerRequestDetail not found for the given id.');
            }

            if (empty($influencerRequestDetail->media)) {
                \Log::error('initiateInfluencerCampaignSubmission: media is empty or invalid', [
                    'id' => $influencerRequestDetailId,
                    'auth_id' => Auth::id(),
                    'influencerRequestDetail' => $influencerRequestDetail
                ]);

                throw new \Exception('Invalid or missing media for influencer campaign submission.');
            }

            $influencerUserId = null;
            if (
                empty($influencerRequestDetail->influencerdetails) ||
                empty($influencerRequestDetail->influencerdetails->user_id)
            ) {
                \Log::error('initiateInfluencerCampaignSubmission: Missing influencerdetails or user_id', [
                    'id' => $influencerRequestDetailId,
                    'auth_id' => Auth::id(),
                    'influencerRequestDetail' => $influencerRequestDetail
                ]);

                throw new \Exception('Something went wrong. Please try again.');
            }

            $influencerUserId = $influencerRequestDetail->influencerdetails->user_id;

            if (empty($influencerUserId)) {
                \Log::error('initiateInfluencerCampaignSubmission: influencerUserId is empty', [
                    'id' => $influencerRequestDetailId,
                    'auth_id' => Auth::id(),
                    'influencerRequestDetail' => $influencerRequestDetail
                ]);
                throw new \Exception('Invalid influencer user id.');
            }

            $socialConnects = SocialConnect::where('user_id', $influencerUserId)->get();
            foreach ($socialConnects as $socialConnect) {
                if (
                    $socialConnect->token != null &&
                    $socialConnect->media == $influencerRequestDetail->media
                ) {
                    if ($socialConnect->token_secret != null) {
                        if ($socialConnect->media == 'instagram') {
                            $this->importInstagramPostsAndInsights($socialConnect, $influencerRequestDetail);
                        }
                    }
                }
            }

            $validSocialPostCount = 0;

            $showTitle = $influencerRequestDetail->post_type;
            if ($influencerRequestDetail->post_content_type != '') {
                $showTitle .= " {$influencerRequestDetail->post_content_type}";
            }

            $socialPosts = SocialPost::where('user_id', Auth::id())
                ->where('media', $influencerRequestDetail->media)
                ->orderBy('published_at', 'desc')
                ->get();
            
            $renderPartials = [];
            
            foreach ($socialPosts as $socialPost) {
                $renderPartialName = '';

                $use_timezone = new \DateTimeZone(@Session::get('timezone') ? Session::get('timezone') : 'UTC');

                $published_at = \DateTime::createFromFormat('Y-m-d H:i:s', $socialPost->published_at, $use_timezone);
                $published_at = $published_at->format('Y-m-d H:i:s');

                $created_at = \DateTime::createFromFormat('Y-m-d H:i:s', $influencerRequestDetail->created_at, $use_timezone);
                $created_at = $created_at->format('Y-m-d H:i:s');

                $now = new \DateTime('now', $use_timezone);
                $now = $now->format('Y-m-d H:i:s');

                // The post time conditions do not met, so we do not consider this
                // social post posted for this campaign.
                if (!($published_at >= $created_at && $published_at <= $now)) {
                    continue;
                }

                if ($influencerRequestDetail->post_type == 'Boost me') {
                    $renderPartialName = $influencerRequestDetail->post_type;
                } elseif ($influencerRequestDetail->post_type == 'Reaction video') {
                    if ($socialPost->influencer_request_accept_id == '' && isset($socialPost->type) && $socialPost->type == 'video') {
                        $renderPartialName = $influencerRequestDetail->post_type;
                    }

                    if ($socialPost->influencer_request_accept_id == 'reel' && $influencerRequestDetail->advertising == 'Reel') {
                        $renderPartialName = $influencerRequestDetail->post_type;
                    }

                    if (
                        $socialPost->influencer_request_accept_id == 'story' &&
                        $socialPost->type == 'video' &&
                        $influencerRequestDetail->advertising == 'Story - Video'
                    ) {
                        $renderPartialName = $influencerRequestDetail->post_type;
                    }
                } elseif ($influencerRequestDetail->post_type == 'Survey') {
                    if (
                        $socialPost->influencer_request_accept_id == 'story' ||
                        $socialPost->influencer_request_accept_id == 'reel'
                    ) {
                        $renderPartialName = $influencerRequestDetail->post_type;
                    }
                }

                if ($renderPartialName != '') {
                    $validSocialPostCount += 1;
                    $renderPartials[$renderPartialName][] = $socialPost;
                }
            }

            return \Response::json(
                \View::make(
                    'components.partials.influencer-initiate-submission',
                    compact('socialPosts', 'influencerRequestDetail', 'validSocialPostCount', 'renderPartials', 'showTitle')
                )->render()
            );

        } catch (Exception $e) {
            return response()->json(['status' => 'error', 'message' => $e->getMessage()], 500);
        }
    }

    public function confirmInfluencerCampaignSubmission(Request $request)
    {
        try {
            if ($request->task != '') {
                $count_rows_updated = InfluencerRequestDetail::where('id', $request->influencer_request_id)->update([
                    'social_post_id' => $request->post_id,
                    'read_status' => 'post_submit',
                    'read_at' => NULL,
                    'tasks' => implode(',', $request->task)
                ]);
            } else {
                $count_rows_updated = InfluencerRequestDetail::where('id', $request->influencer_request_id)->update([
                    'social_post_id' => $request->post_id,
                    'read_status' => 'post_submit',
                    'read_at' => NULL
                ]);
            }

            // Sample $request data submitted
            // array(10) {
            //     ["_token"]=> string(40) "8zHhqjPsItl5dZheQMEbR9nsDuysyRyWenMsetb7"
            //     ["influencer_request_id"]=> string(3) "343"
            //     ["post_id"]=> string(2) "25"
            //     ["advertising"]=> string(4) "Reel"
            //     ["media"]=> string(9) "instagram"
            //     ["media_url"]=> string(43) "social_pics/18042587006415999_instagram.mp4"
            //     ["published_at"]=> string(19) "2025-05-24 13:26:02"
            //     ["task"]=> array(4) { [0]=> string(2) "71" [1]=> string(3) "211" [2]=> string(3) "212" [3]=> string(3) "213" }
            //     ["client_rights_terms"]=> string(2) "on"
            //     ["confirm"]=> string(6) "Submit"
            // }

            // Sample $request data for survey (with file upload)
            // Array
            // (
            //     [_token] => CDrMFGOxSIrIvAy61E01v51ZLqgYRJsw71kEfzbZ
            //     [influencer_request_id] => 503
            //     [post_id] => 1379
            //     [advertising] => Story - Picture
            //     [media] => instagram
            //     [media_url] => social_pics/18142224715389498_instagram.mp4
            //     [published_at] => 2025-06-08 09:13:52
            //     [task] => Array
            //         (
            //             [0] => 175
            //             [1] => 177
            //             [2] => 225
            //             [3] => 226
            //             [4] => 246
            //             [5] => 247
            //         )

            //     [client_rights_terms] => on
            //     [confirm] => Submit
            //     [survey_image] => Illuminate\Http\UploadedFile Object
            //         (
            //             [test:Symfony\Component\HttpFoundation\File\UploadedFile:private] => 
            //             [originalName:Symfony\Component\HttpFoundation\File\UploadedFile:private] => 9wgbmj.jpg
            //             [mimeType:Symfony\Component\HttpFoundation\File\UploadedFile:private] => image/jpeg
            //             [error:Symfony\Component\HttpFoundation\File\UploadedFile:private] => 0
            //             [hashName:protected] => 
            //             [pathName:SplFileInfo:private] => /private/var/folders/xg/kz29t8r14l11lssr39mhg0s80000gn/T/phpZDMaMF
            //             [fileName:SplFileInfo:private] => phpZDMaMF
            //         )

            // )

            $influencerRequestDetail = InfluencerRequestDetail::where('id', $request->influencer_request_id)->first();

            if (!$influencerRequestDetail || empty($influencerRequestDetail->user_id)) {
                \Log::error('InfluencerRequestDetail not found', [
                    'influencer_request_id' => $request->influencer_request_id,
                    'request_data' => $request->all(),
                    'auth_id' => Auth::id(),
                    'user_id' => $influencerRequestDetail->user_id ?? null
                ]);
                throw new \Exception('InfluencerRequestDetail not found for the given influencer_request_id.');
            }

            $customer = User::whereId($influencerRequestDetail->user_id)->first();
            if (!$customer) {
                \Log::error('Customer not found for influencerRequestDetail', [
                    'influencer_request_id' => $request->influencer_request_id,
                    'user_id' => $influencerRequestDetail->user_id,
                    'request_data' => $request->all(),
                    'auth_id' => Auth::id(),
                ]);
                throw new \Exception('Customer not found for the given influencer request.');
            }

            if (!empty($influencerRequestDetail->influencerdetails) && !empty($influencerRequestDetail->influencerdetails->user_id)) {
                $influencer = User::whereId($influencerRequestDetail->influencerdetails->user_id)->first();
            } else {
                $influencer = User::find(Auth::id());
            }

            if (!$influencer || $influencer->user_type != 'influencer') {
                \Log::info('Influencer confirmSocial fallback', [
                    'customer_id' => $customer->id ?? null,
                    'influencer_id' => $influencer->id ?? null,
                    'auth_id' => Auth::id(),
                    'influencer_user_type' => $influencer->user_type ?? null,
                ]);

                throw new Exception('Failed to load influencer details.');
            }

            if ($request->file('survey_image')) {
                $path = $request->file('survey_image')->store('social_pics');
                $influencerRequestDetail->update([
                    'insight' => $path
                ]);
            }

            $influencerRequestDetailAll = InfluencerRequestDetail::where('compaign_id', $influencerRequestDetail->compaign_id)->get();
            $count = 0;

            foreach ($influencerRequestDetailAll as $all) {
                $influencerAcceptAll = InfluencerRequestDetail::where('id', $all->id)->where('social_post_id', '!=', null)->first();
                if (isset($influencerAcceptAll)) {
                    $count++;
                }
            }

            if ($count == 1) {
                dispatch(new NewConfirmPostInfluencer($customer, $influencerRequestDetail, $influencer));
                $customer->notify(new ConfirmPostInfluencer($customer, $influencerRequestDetail, $influencer));
            } else if ($influencerRequestDetailAll->count() == $count) {
                dispatch(new NewtheInfluencersHaveDoneTheirJob($customer, $influencerRequestDetail, $influencer));
            }

            $results = AdminGamification::where('select_type', 'Point-Rules')->first();
            $created_at = strtotime($influencerRequestDetail->created_at);
            $updated_at = strtotime($influencerRequestDetail->updated_at);
            $datediff = $updated_at - $created_at;
            $days =  round($datediff / (60 * 60 * 24));
            if ($days < ($influencerRequestDetail->time) / 2  && $influencerRequestDetail->social_post_id != null && $influencerRequestDetail->refund_reason == null) {
                Statistic::create([
                    'user_id' => Auth::id(),
                    'points' => $results->points_completed_time,
                    'type' => '1',
                    'title' =>  '[' . $influencerRequestDetail->compaign_id . ']</br>' . $results->points_completed_time . ' points gained for submitting on time',
                    'date' => date('Y-m-d H:i:s'),
                ]);
            } elseif ($days <  $influencerRequestDetail->time  && $influencerRequestDetail->social_post_id != null   && $influencerRequestDetail->refund_reason == null) {
                Statistic::create([
                    'user_id' => Auth::id(),
                    'points' => $results->points_half_time,
                    'type' => '1',
                    'title' =>  '[' . $influencerRequestDetail->compaign_id . ']</br>' . $results->points_half_time . '  points gained for submitting half the time',
                    'date' => date('Y-m-d H:i:s'),
                ]);
            }

            return back()->with('success', 'Social post confirmed successfully.');
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }    

    private function importInstagramPostsAndInsights($socialConnect, $influencerRequestDetail) {
        // $appEnv = env('APP_ENV');
        // $appUrl = env('APP_URL');
        // if (
        //     $appEnv === 'localhost' ||
        //     ($appEnv === 'local' && strpos($appUrl, 'dev1') !== false)
        // ) {
        //     \App\Models\SocialPost::createFakeInstagramPost($influencerId = $socialConnect->user_id);
        //     return;
        // }

        $postImporter = new InsightsForTypePost($socialConnect, $influencerRequestDetail);
        $postImporter->importSocialMediaPost();

        $storyImporter = new InsightsForTypeStory($socialConnect, $influencerRequestDetail);
        $storyImporter->importSocialMediaStory();
    }
}
