<?php

/**
 * @see https://github.com/InnoManic-Org/ClickItFame/issues/570
 */

namespace App\Helpers\Instagram;

use App\Models\SocialPost;
use App\Models\SocialConnect;
use App\Models\InfluencerRequestDetail;
use Carbon\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class InsightsForTypePost
{
    protected SocialConnect $socialConnect;
    protected InfluencerRequestDetail $influencerRequestDetail;

    const FIELDS = ['id', 'caption', 'media_type', 'thumbnail_url', 'media_url', 'permalink', 'timestamp'];
    const METRICS = ['likes', 'comments', 'views', 'reach', /*'shares'*/];

    public function __construct(SocialConnect $socialConnect, InfluencerRequestDetail $influencerRequestDetail)
    {
        $this->socialConnect = $socialConnect;
        $this->influencerRequestDetail = $influencerRequestDetail;
    }

    public function importSocialMediaPost(): void
    {
        $baseApiUrl = 'https://graph.facebook.com/v18.0/' . $this->socialConnect->token_secret . '/media?&access_token=' . $this->socialConnect->token;
        $callApiUrl = $baseApiUrl . '&fields=' . implode(',', self::FIELDS);

        try {
            $response = Http::get($callApiUrl);

            if (!$response->successful()) {
                Log::error('Failed to fetch Instagram posts', [
                    'user_id' => $this->socialConnect->user_id,
                    'status' => $response->status(),
                    'response' => $response->body()
                ]);
                return;
            }

            $socialPostData = $response->json();
        } catch (\Exception $e) {
            Log::error('Exception while fetching Instagram posts', [
                'user_id' => $this->socialConnect->user_id,
                'error' => $e->getMessage()
            ]);
            return;
        }

        if (isset($socialPostData['data'])) {
            foreach ($socialPostData['data'] as $socialPostDataItem) {
                $this->processSocialPostItem($socialPostDataItem);
            }
        }
    }

    private function processSocialPostItem(array $socialPostDataItem): void
    {
        $postType = '';
        if (str_contains($socialPostDataItem['permalink'] ?? '', '/reel/')) {
            $postType = 'reel';
        }

        if (empty($socialPostDataItem['id'])) {
            Log::error('Instagram post missing ID during submission processing', [
                'user_id' => $this->socialConnect->user_id,
                'influencer_request_id' => $this->influencerRequestDetail->id,
                'post_data' => $socialPostDataItem,
                'permalink' => $socialPostDataItem['permalink'] ?? 'N/A',
                'media_type' => $socialPostDataItem['media_type'] ?? 'N/A',
                'timestamp' => $socialPostDataItem['timestamp'] ?? 'N/A'
            ]);
            return;
        }

        $insightUrl = 'https://graph.facebook.com/v18.0/' . $socialPostDataItem['id'] . '/insights?access_token=' . $this->socialConnect->token;
        $insightUrl .= '&metric=' . implode(',', self::METRICS);

        try {
            $insightResponse = Http::get($insightUrl);

            if (!$insightResponse->successful()) {
                Log::warning('Failed to fetch Instagram post insights', [
                    'post_id' => $socialPostDataItem['id'],
                    'user_id' => $this->socialConnect->user_id,
                    'status' => $insightResponse->status()
                ]);
                $insightData = [];
            } else {
                $insightData = $insightResponse->json();
            }
        } catch (\Exception $e) {
            Log::error('Exception while fetching Instagram post insights', [
                'post_id' => $socialPostDataItem['id'],
                'user_id' => $this->socialConnect->user_id,
                'error' => $e->getMessage()
            ]);
            $insightData = [];
        }

        $preparedMetricsData = [];

        if (isset($insightData['data'])) {
            $preparedMetricsData['complete__' . date('Y_m_d_H_i_s')] = $insightData;
            foreach ($insightData['data'] as $insightDataItem) {
                $preparedMetricsData[$insightDataItem['name']] = $insightDataItem['values'][0]['value'] ?? 0;
            }
        }

        $postTimestamp = Carbon::parse($socialPostDataItem['timestamp']);
        $requestCreatedAt = Carbon::parse($this->influencerRequestDetail->created_at);
        $today = Carbon::today();

        if ($postTimestamp->greaterThanOrEqualTo($requestCreatedAt) && $postTimestamp->lessThanOrEqualTo($today)) {
            $filepath = '';
            $contentType = 'video';

            if (isset($socialPostDataItem['media_url'])) {
                try {
                    $fileContents = file_get_contents($socialPostDataItem['media_url']);
                    $filename = $socialPostDataItem['id'] . '_instagram';

                    $fileExt = '.mp4';
                    if (
                        ($socialPostDataItem['media_type'] ?? '') == 'IMAGE' ||
                        ($socialPostDataItem['media_type'] ?? '') == 'CAROUSEL_ALBUM'
                    ) {
                        $fileExt = '.jpg';
                        $contentType = 'photo';
                    }

                    $filenameWithExt = $filename . $fileExt;
                    $filepath = 'social_pics/' . $filenameWithExt;

                    Storage::disk('public')->put($filepath, $fileContents);
                } catch (\Exception $e) {
                    Log::error('Failed to download Instagram media file', [
                        'post_id' => $socialPostDataItem['id'],
                        'media_url' => $socialPostDataItem['media_url'] ?? 'N/A',
                        'error' => $e->getMessage()
                    ]);
                }
            }

            $socialPost = SocialPost::where('user_id', $this->socialConnect->user_id)
                ->where('media', 'instagram')
                ->where('post_id', $socialPostDataItem['id'])
                ->first();

            $postData = [
                'influencer_request_accept_id' => $postType,
                'text' => $socialPostDataItem['caption'] ?? null,
                'link' => $filepath,
                'type' => $contentType,
                'published_at' => $postTimestamp->format('Y-m-d H:i:s'),
                'thumbnail' => $socialPostDataItem['permalink'] ?? null,
                'insights' => $preparedMetricsData
            ];

            if ($socialPost && count($preparedMetricsData) > 0) {
                // Only update when we have $preparedMetricData
                $socialPost->update($postData);
            } else {
                $postData['user_id'] = $this->socialConnect->user_id;
                $postData['media'] = 'instagram';
                $postData['post_id'] = $socialPostDataItem['id'];

                SocialPost::create($postData);
            }
        }
    }
}